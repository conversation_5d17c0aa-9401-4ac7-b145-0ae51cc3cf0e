<script setup>
import { onMounted, ref, computed } from 'vue';
import axios from 'axios';
import Loader from "./components/Loader.vue";

const containerId = document.querySelector('#vuespa').dataset.containerId;
const products = ref([]);
const loading = ref(true);
const hovered = ref();

const productLookup = computed(() => {
  const lookup = {};
  products.value.forEach(product => {
    const diameter = product.options.find(o => o.code === 'diameter')?.value;
    const length = product.options.find(o => o.code === 'length')?.value;

    if (diameter && length) {
      lookup[`${diameter}-${length}`] = product;
    }
  });
  return lookup;
});

const diameters = computed(() => {
  return getUniqueSortedOptions(products.value, 'diameter');
});

const lengths = computed(() => {
  return getUniqueSortedOptions(products.value, 'length');
});

const sizesAvailable = computed(() => diameters.value.length > 0 && lengths.value.length > 0);

function getUniqueSortedOptions(products, optionCode) {
  const options = products.flatMap(p =>
      p.options.filter(o => o.code === optionCode)
  );

  const values = options
      .map(o => o.value)
      .filter(Boolean)
      .map(Number);

  return [...new Set(values)].sort((a, b) => a - b);
};

function isHighlighted(diameter, length) {
  if (!hovered.value) return false;

  return (hovered.value.diameter === diameter && hovered.value.length >= length)
      || (hovered.value.length === length && hovered.value.diameter >= diameter);
};

function getProduct(diameter, length) {
  return productLookup.value[`${diameter}-${length}`];
}

function handleClick(diameter, length) {
  const product = getProduct(diameter, length);
  console.log("product clicked:", product);
}

async function loadProducts() {
  try {
    const response = await axios.get(`?action=getProductsAjax&containerId=${containerId}`);
    products.value = response.data;
  } catch (error) {
    console.error('Error loading products:', error);
  } finally {
    loading.value = false;
  }
}

onMounted(loadProducts);
</script>

<template>
  <Loader v-if="loading" />

  <table v-else-if="sizesAvailable" class="product-grid">
    <tbody>
    <tr>
      <td class="header">I/ø</td>
      <td
          v-for="length in lengths"
          :key="length"
          class="header"
      >
        {{ length }}
      </td>
    </tr>
    <tr v-for="diameter in diameters" :key="diameter">
      <td class="header">{{ diameter }}</td>
      <td
          v-for="length in lengths"
          :key="`${diameter}-${length}`"
          :class="{
            'cell': true,
            'has-product': getProduct(diameter, length),
            'highlight': isHighlighted(diameter, length)
          }"
          @click="handleClick(diameter, length)"
          @mouseenter="hovered = { diameter, length }"
          @mouseleave="hovered = null"
      >
          <span v-if="getProduct(diameter, length)" class="product-code">
            {{ diameter }} × {{ length }}
          </span>
      </td>
    </tr>
    </tbody>
  </table>
  <div v-else>Geen maten beschikbaar</div>
</template>

<style scoped lang="scss">
tbody {
  border: none !important;
}

.product-grid td {
  text-align: center;
}

.header {
  background-color: var(--color-primary);
  color: var(--color-white);
}

.cell {
  background-color: var(--color-gray);

  &.has-product {
    background-color: var(--color-light-blue);
    cursor: pointer;

    &.highlight {
      background-color: var(--color-primary);
    }

    &:hover {
      background-color: var(--color-bg-danger);
      color: white;

      .product-code {
        opacity: 1;
      }
    }
  }

  &.highlight {
    background-color: var(--color-medium-blue);
  }
}

.product-code {
  font-size: 12px;
  opacity: 0;
}
</style>