<script setup>
import { computed } from 'vue';

const props = defineProps({
  product: {
    type: Object,
    default: null
  },
  isOpen: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['close', 'addToBasket']);

const diameter = computed(() => {
  return props.product?.options.find(o => o.code === 'diameter')?.value;
});

const length = computed(() => {
  return props.product?.options.find(o => o.code === 'length')?.value;
});

function closeModal() {
  emit('close');
}

function addToBasket() {
  emit('addToBasket', props.product);
  closeModal();
}

function handleBackdropClick(event) {
  if (event.target === event.currentTarget) {
    closeModal();
  }
}
</script>

<template>
  <div v-if="isOpen && product" class="modal-backdrop" @click="handleBackdropClick">
    <div class="modal-content">
      <div class="modal-header">
        <h2>Product Details</h2>
        <button class="close-button" @click="closeModal">&times;</button>
      </div>
      
      <div class="modal-body">
        <div class="product-info">
          <h3>{{ product.name || `Bout ${diameter} × ${length}` }}</h3>
          
          <div class="product-details">
            <div class="detail-row">
              <span class="label">Product Code:</span>
              <span class="value">{{ product.code || `${diameter}x${length}` }}</span>
            </div>
            
            <div class="detail-row">
              <span class="label">Diameter:</span>
              <span class="value">{{ diameter }}mm</span>
            </div>
            
            <div class="detail-row">
              <span class="label">Length:</span>
              <span class="value">{{ length }}mm</span>
            </div>
            
            <div v-if="product.description" class="detail-row">
              <span class="label">Description:</span>
              <span class="value">{{ product.description }}</span>
            </div>
            
            <div v-if="product.price" class="detail-row">
              <span class="label">Price:</span>
              <span class="value">€{{ product.price }}</span>
            </div>
            
            <div v-if="product.stock !== undefined" class="detail-row">
              <span class="label">Stock:</span>
              <span class="value">{{ product.stock }} pieces</span>
            </div>
          </div>
        </div>
      </div>
      
      <div class="modal-footer">
        <button class="btn btn-secondary" @click="closeModal">
          Cancel
        </button>
        <button class="btn btn-primary" @click="addToBasket">
          Add to Basket
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e5e5e5;
  
  h2 {
    margin: 0;
    color: var(--color-primary, #333);
  }
}

.close-button {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &:hover {
    color: #333;
  }
}

.modal-body {
  padding: 1.5rem;
}

.product-info {
  h3 {
    margin: 0 0 1rem 0;
    color: var(--color-primary, #333);
  }
}

.product-details {
  .detail-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.75rem;
    
    .label {
      font-weight: 600;
      color: #666;
    }
    
    .value {
      color: #333;
    }
  }
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  border-top: 1px solid #e5e5e5;
  background-color: #f8f9fa;
}

.btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
  
  &.btn-secondary {
    background-color: #6c757d;
    color: white;
    
    &:hover {
      background-color: #5a6268;
    }
  }
  
  &.btn-primary {
    background-color: var(--color-primary, #007bff);
    color: white;
    
    &:hover {
      background-color: var(--color-primary-dark, #0056b3);
    }
  }
}
</style>
