<script setup>
import FilterBox from "./FilterBox.vue";
import { onMounted, ref, watch } from "vue";
import axios from "axios";
import { useItemStore } from "../stores/itemStore.js";

const props = defineProps(['categoryId']);
const itemStore = useItemStore();

const categories = ref();

// dummy data
const qualityFilters = ref(['/6/', '10.9', '100', '12.9', '45H']);
const threadFilters = ref(['L', 'M']);
const treatmentFilters = ref(['ZN']);

// geselecteerde filters
const selectedQuality = ref(JSON.parse(localStorage.getItem('selectedQuality') || '[]'));
const selectedThread = ref(JSON.parse(localStorage.getItem('selectedThread') || '[]'));
const selectedTreatment = ref(JSON.parse(localStorage.getItem('selectedTreatment') || '[]'));

// opslag in localStorage bij wijziging
watch(selectedQuality, (value) => {
  localStorage.setItem('selectedQuality', JSON.stringify(value));
}, { deep: true });
watch(selectedThread, (value) => {
  localStorage.setItem('selectedThread', JSON.stringify(value));
}, { deep: true });
watch(selectedTreatment, (value) => {
  localStorage.setItem('selectedTreatment', JSON.stringify(value));
}, { deep: true });

onMounted(async () => {
  const response = await axios.get('?action=getCategoriesAjax')
  categories.value = response.data.categories;
})
</script>

<template>
  <div>
    <filter-box title="Alle producten">
      <a v-for="category in categories" class="filter" :class="{ active: category.id == props.categoryId }" :href="category.content.url" :title="category.content.name">
        {{ category.content.name }}
      </a>
    </filter-box>
    <template v-if="itemStore.products.length">
      <filter-box title="Kwaliteitsklasse" collapse>
        <a v-for="filter in qualityFilters" :key="filter" class="filter" :title="filter">
          <input :value="filter" v-model="selectedQuality" type="checkbox" class="custom-checkbox" :id="filter"><label class="filter-label" :for="filter">{{ filter }}</label>
        </a>
      </filter-box>
      <filter-box title="Draad" collapse>
        <a v-for="filter in threadFilters" :key="filter" class="filter" :title="filter">
          <input :value="filter" v-model="selectedThread" type="checkbox" class="custom-checkbox" :id="filter"><label class="filter-label" :for="filter">{{ filter }}</label>
        </a>
      </filter-box>
      <filter-box title="Behandeling" collapse>
        <a v-for="filter in treatmentFilters" :key="filter" class="filter" :title="filter">
          <input :value="filter" v-model="selectedTreatment" type="checkbox" class="custom-checkbox" :id="filter"><label class="filter-label" :for="filter">{{ filter }}</label>
        </a>
      </filter-box>
    </template>
  </div>
</template>

<style scoped>
.filter-label {
  display: inline-block;
  width: calc(100% - 22px);
  padding-left: 1em;
  cursor: pointer;
}
</style>