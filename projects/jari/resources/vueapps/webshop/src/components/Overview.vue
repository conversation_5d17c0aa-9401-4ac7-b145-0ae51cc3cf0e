<script setup>
import {ref, onMounted, watch} from 'vue';
import axios from 'axios';
import Card from "./Card.vue";
import { usePagerStore } from "../stores/pagerStore.js";
import Pager from "./Pager.vue";
import { useLoadingStore } from "../stores/loadingStore";
import { useItemStore } from "../stores/itemStore.js";
import Loader from "./Loader.vue";

const props = defineProps(['categoryId']);

const itemStore = useItemStore();
const pagerStore = usePagerStore();
pagerStore.context = props.categoryId;

const loadingStore = useLoadingStore();

onMounted(async () => {
  await fetchProductsAndCategories();
})
watch(() => pagerStore.pageNumber, async () => {
  await fetchProductsAndCategories();
})

async function fetchProductsAndCategories() {
  loadingStore.isLoading = true;
  try {
    const productsResponse = await axios.get(`?action=getProductContainersAjax&categoryId=${props.categoryId}&pageNum=${pagerStore.pageNumber}`)
    itemStore.products = productsResponse.data.products;
    if (itemStore.products.length) pagerStore.pager = productsResponse.data.pager;

    const categoriesResponse = await axios.get(`?action=getCategoriesAjax&categoryId=${props.categoryId}&pageNum=${pagerStore.pageNumber}`)
    itemStore.categories = categoriesResponse.data.categories;
    if (itemStore.categories.length) pagerStore.pager = categoriesResponse.data.pager;

  } catch (error) {
    console.error('Error fetching items:', error);
  } finally {
    loadingStore.isLoading = false;
  }
}
</script>

<template>
  <div class="overview">
    <loader v-if="loadingStore.isLoading"/>
    <card
        v-for="product in itemStore.products"
        :key="product.id"
        :name="product.content.name"
        :url="product.content.url"
        :imageUrl="product.imageUrl"
        type="Product"
    />
    <card
        v-for="category in itemStore.categories"
        :key="category.id"
        :name="category.content.name"
        :url="category.content.url"
        :imageUrl="category.imageUrl"
        type="Categorie"
    />
    <pager/>
  </div>
</template>

<style scoped>
.overview {
  position: relative;
  min-height: 400px;
}
</style>