.product-image {
  aspect-ratio: 5 / 4;
  border-radius: 10px;
  border: 1px solid rgba(10, 101, 153, .2);

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}

.products-attributes {
  display: flex;
  flex-wrap: wrap;
  gap: 10px 0;
  padding: 40px 45px;
  background: var(--color-gray);
  border-radius: 10px;
  margin: 1.5em 0;

  .attribute-row {
    width: 50%;
    display: flex;
    padding-right: 20px;
    letter-spacing: 0.03em;
    flex-wrap: wrap;
  }

  .attribute-label {
    font-weight: 600;
    min-width: 175px;
    flex-shrink: 0;
    text-align: left;
    white-space: normal;
  }

  .attribute-value {
    flex: 1 1 auto;
    text-align: left;
    white-space: normal;
  }
}

div:has(.products-attributes) {
  align-self: start;
}

.products-details > div:not(:last-child) {
  margin-bottom: 3em;
}

.table-responsive {
  width: 100%;
  overflow: auto;

  table {
    border-collapse: collapse;
    text-align: left;
    table-layout: fixed;
    min-width: 100%;
    margin-bottom: .5em;

    strong {
      color: var(--color-primary);
    }

    tbody {
      display: flex;
      flex-direction: column;
      border: 1px solid var(--color-blue);
      border-radius: 10px;

      tr {
        width: 100%;
        display: flex;
        font-size: 16px;
        color: var(--color-blue);
        font-weight: 400;
        letter-spacing: .03em;
        border-bottom: 1px solid rgba(8, 59, 88, .2);
      }

      tr:first-child {
        border-bottom: 1px solid var(--color-blue);
        font-weight: 600;
        color: var(--color-primary);
      }

      td {
        flex: 1;
        border: none;
        padding: 10px 15px;
        min-width: 103px;
      }

      td:not(:last-child) {
        border-right: 1px solid rgba(8, 59, 88, .2);
      }
    }
  }
}

.carousel {
  display: flex;
  flex-wrap: nowrap;
  overflow-x: auto;
  gap: 2rem;
  width: 100%;
  padding-bottom: 1rem;

  .card {
    flex-shrink: 0;
    width: 300px;
    height: 100% !important;
    h2 {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}

.table-responsive, .carousel {
  &::-webkit-scrollbar {
    height: 8px;
    background-color: transparent;
  }
  &::-webkit-scrollbar-thumb {
    border-radius: 10px;
    background-color: var(--color-primary);
  }
}

@media (max-width: 640px) {
  .products-attributes .attribute-row {
    width: 100%;
    padding-right: 0;
  }
}