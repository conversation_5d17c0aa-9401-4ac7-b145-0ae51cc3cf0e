<?php

  namespace domain\aboservice\service;

  use domain\aboservice\exception\AboserviceException;
  use FileHelper;
  use Guzzle<PERSON>ttp\Client;
  use GuzzleHttp\Exception\GuzzleException;
  use GuzzleHttp\Exception\RequestException;
  use Guz<PERSON>Http\Psr7;

  class AboserviceApi {

    private string $url;
    private \Setting $username;
    private \Setting $password;
    private \Setting $access_token;
    private \Setting $access_token_datetime;
    private string $expires_in;

    public function __construct(\Setting $username, \Setting $password, \Setting $accessToken, \Setting $accessTokenDatetime) {
      $config = \Config::get('ABOSERVICE_API');
      $this->url = $config['url'];
      $this->expires_in = $config['tokenExp'];

      if (empty($username->value)) {
        throw new AboserviceException("Gebruikersnaam is leeg. Stel deze in via de beheeromgeving.");
      }

      if (empty($password->value)) {
        throw new AboserviceException("Wachtwoord is leeg. Stel deze in via de beheeromgeving.");
      }

      $this->username = $username;
      $this->password = $password;
      $this->access_token = $accessToken;
      $this->access_token_datetime = $accessTokenDatetime;
    }

    protected function request($method, $action, $parameters = []) {
      if ($this->accessTokenHasToBeRefreshed()) {
        $this->refreshAccessToken();
      }

      try {
        $url = $this->url . '/v1.0/' . $action;

        $client = new Client(['verify' => false]);
        $res = $client->request($method, $url, [
          'headers' => [
            'Authorization' => 'Bearer ' . $this->access_token->value,
            'Content-Type'  => 'application/json',
            'Accept'        => 'application/json',
          ],
          'json'    => $method !== 'GET' ? $parameters : [],
        ]);

        $json = $res->getBody()->getContents();

        if ($json == "") {
          $message = "Lege response Aboservice API. Url: " . $url;
          logToFile("aboservice-error", $message);
          throw new AboserviceException($message);
        }

        $body = json_decode($json, true);
        if (isset($body['error'])) {
          // @todo check what errors can be returned and act accordingly
          // invalid key =>
          dumpe($body);
        }

        return $body;
      }
      catch (RequestException $e) {
        logToFile("aboservice-error", $e->getMessage() . "\n" . Psr7\Message::toString($e->getRequest()));
        if ($e->hasResponse()) {
          logToFile("aboservice-error", Psr7\Message::toString($e->getResponse()));
        }
      }
      catch (GuzzleException $e) {
        logToFile('aboservice-error', 'Request: ' . $action . ' ' . print_r($parameters, true) . '\n' . $e->getTraceAsString());
      }

      return false;
    }

    public function get($action, $parameters = []) {
      return $this->request('GET', $action, $parameters);
    }

    public function post($action, $parameters) {
      return $this->request('POST', $action, $parameters);
    }

    public function put($action, $parameters) {
      return $this->request('PUT', $action, $parameters);
    }

    /**
     * Token has to be refreshed
     * @return bool $hastoberefreshed
     */
    public function accessTokenHasToBeRefreshed(): bool {
      if (empty($this->access_token_datetime->value)) return true;
      return time() > strtotime("+ " . $this->expires_in . " SECONDS", strtotime($this->access_token_datetime->value));
    }

    public function refreshAccessToken(): bool {

      try {
        $client = new Client(['verify' => false]);
        $response = $client->request('POST', $this->url . '/v1.0/login', [
          'headers' => [
            'Authorization' => 'Basic ' . base64_encode($this->username->value . ':' . \EncryptionHelper::decrypt($this->password->value)),
            'Content-Type'  => 'application/x-www-form-urlencoded',
          ],
          'body'    => http_build_query([]),
        ]);

        if ($response->getStatusCode() == 200) {
          $response = json_decode($response->getBody()->getContents());

          if (isset($response->error) && $response->error === 'nok') { // invalid username/password
            logToFile("aboservice-error", "Invalid username or password.");
            throw new AboserviceException("Authenticatie fout: Incorrecte gebruikersnaam of wachtwoord.");
          }

          $this->access_token->value = $response->token;
          $this->access_token->save();

          $this->access_token_datetime->value = date('Y-m-d H:i:s');
          $this->access_token_datetime->save();

          return true;
        }
      }
      catch (RequestException $e) {
        logToFile("aboservice-error", $e->getMessage() . "\n" . Psr7\Message::toString($e->getRequest()));
        if ($e->hasResponse()) {
          logToFile("aboservice-error", Psr7\Message::toString($e->getResponse()));
        }
        throw new AboserviceException("RefreshAccessToken fout: " . $e->getMessage() . ' ' . $e->getCode());
      }
      catch (GuzzleException $e) {
        logToFile("aboservice-error", $e->getTraceAsString());
      }

      // No exception? Check if your IP is whitelisted in the cloudservice
      // current whitelist: GSD office
      throw new AboserviceException("RefreshAccessToken fout: koppel Aboservice opnieuw aan de applicatie");
    }

    public function getCategoryGroups() {
      return $this->get('files/groepen');
    }

    /**
     * @param $groups
     * @return array
     */
    public static function buildCategoryTreeFromGroups($groups): array {
      $categoryTree = [];
      foreach ($groups as $group) {
        array_walk($group, function (&$value) {
          $value = trim($value);
        }); // cleanup

        if (isset($group['cat']) && $group['cat'] !== '') { // layer 3 => product container
          $familyUid = substr($group['uid'], 0, 6);
          $categoryTree[$familyUid]['children'][$group['parentuid']]['children'][$group['uid']] = $group;
        }
        elseif (isset($group['subfamilie']) && $group['subfamilie'] !== '') { // layer 2 => subcategory
          $categoryTree[$group['parentuid']]['children'][$group['uid']] = $group;
        }
        else { // layer 1 => category
          $categoryTree[$group['uid']] = $group;
        }
      }

      return $categoryTree;
    }

    public function exportCategoryGroups() {
      $groups = $this->getCategoryGroups()['data'];
      $data = [['id', 'familie', 'subfamilie', 'cat', 'subcat', 'uid', 'parentuid', 'omschr']];
      foreach ($groups as $group) {
        array_walk($group, 'trim'); // cleanup

        $data[] = [
          $group['id'], // ignore?
          $group['familie'], // layer 1 id
          $group['subfamilie'], //  layer 2 id
          $group['cat'], // layer 3 id
          $group['subcat'], // not used atm
          $group['uid'], // consist of 6 numbers => 1 = 010000, 49 = 049000, 103 = 103000, 7002 = 700200
          $group['parentuid'], // uuid of parent
          $group['omschr'], // description
        ];
      }

      FileHelper::outputArrayToCSV($data, "jari-groups.csv", ";");
      exit;
    }

    /**
     * Get all products that have been assigned to the 'layer 3' category
     * @return false|mixed
     */
    public function getProducts(string $start = '') {
      // while serverload = true, the next page can be requested
      $startQuery = ''; // pagination uses id of last entry (#500) as 'start' of previous page
      if (trim($start) != '') $startQuery = '&start=' . $start;

      return $this->get("tabfiles/stock?key=*cat>” “" . $startQuery);
    }

    /**
     * Get products that have been updated since yesterday
     * @return false|mixed
     */
    public function getChangedProducts() {
      return $this->get("tabfiles/stock?key=*date_chg>date()-1.and.cat>” “");
    }

    /**
     * @param string $productId because of leading zeros we have to use strings
     * @return false|mixed
     */
    public function getProductById(string $productId) {
      return $this->get('tabfiles/stock/' . $productId);
    }

    public function exportProducts() {
      $products = $this->getProducts()['data'];
      $data = [['id', 'art_nummer', 'familie', 'subfamilie', 'omschr_n', 'hoev', 'verkoopa', 'res']];
      foreach ($products as $product) {
        array_walk($product, 'trim'); // cleanup

        $data[] = [
          $product['id'],
          $product['art_nummer'],
          $product['familie'],
          $product['subfamilie'],
          $product['omschr_n'],
          $product['hoev'],
          $product['verkoopa'],
          $product['res'],
        ];
      }

      FileHelper::outputArrayToCSV($data, "jari-products.csv", ";");
      exit;
    }

  }