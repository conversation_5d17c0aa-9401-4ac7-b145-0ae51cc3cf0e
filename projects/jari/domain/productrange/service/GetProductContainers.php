<?php

  namespace domain\productrange\service;

  use DBConn;
  use DbHelper;
  use domain\productrange\collection\ProductContainers;
  use Pager;
  use ProductCont;
  use ProductContContent;

  class GetProductContainers {


    /**
     * Get the products (product containers) from the parent category of the product category
     *
     * @param int $parent_category_id
     * @return array
     */
    public function getOnlineOfParentCategory(int $parent_category_id): array {
      $filter_query = "";
      $filter_query .= "JOIN category ON category.online_custshop = 1 ";
      $filter_query .= "AND (category.id = product_cont.category1_id ";
      $filter_query .= "OR category.id = product_cont.category2_id ";
      $filter_query .= "OR category.id = product_cont.category3_id) ";
      $filter_query .= "JOIN category parent_category ON parent_category.id = category.parent_id AND parent_category.online_custshop = 1 ";
      $filter_query .= "JOIN product_cont_content ON product_cont_content.product_cont_id = product_cont.id ";
      $filter_query .= "AND parent_category.id = " . DbHelper::escape($parent_category_id) . " ";
      $filter_query .= "WHERE product_cont.online = 1 ";
      $filter_query .= "ORDER BY product_cont.sort ASC ";

      return $this->fillContainers($filter_query);
    }


    /**
     * Get the products (product containers) from the product category
     *
     * @param int $parentCategoryId
     * @param Pager|null $pager
     * @return array
     * @throws \GsdException
     */
    public function getOnlineOfCategory(int $parentCategoryId, ?Pager $pager): array {
      $filterQuery = "";
      $filterQuery .= "JOIN category ON category.online_custshop = 1 ";
      $filterQuery .= "AND (category.id = product_cont.category1_id ";
      $filterQuery .= "OR category.id = product_cont.category2_id ";
      $filterQuery .= "OR category.id = product_cont.category3_id) ";
      $filterQuery .= "JOIN product_cont_content ON product_cont_content.product_cont_id = product_cont.id ";
      $filterQuery .= "AND category.id = " . DbHelper::escape($parentCategoryId) . " ";
      $filterQuery .= "WHERE product_cont.online = 1 ";
      $filterQuery .= "ORDER BY product_cont.sort ASC ";
      if ($pager) {
        $count = "SELECT COUNT(product_cont.id) AS count FROM product_cont " . $filterQuery;
        $result = DBConn::db_link()->query($count);
        $row = $result->fetch_assoc();
        if ($row) $pager->count = $row['count'];
        $filterQuery .= $pager->getLimitQuery();
      }

      return $this->fillContainers($filterQuery);
    }

    public function getCategoryList(?array $category_ids): array {
      $product_containers = new ProductContainers();
      $product_containers->selectWithContent();
      $product_containers->joinCategory();
      $product_containers->filterCategory($category_ids);
      $product_containers->joinContent();
      $product_containers->whereNotRemoved();
      $product_containers->orderBy('product_cont.sort', 'ASC');
      return $product_containers->retrieveWithContent();
    }

    public function getAllOnline() {
      $filter_query = "";
      $filter_query .= "JOIN category ON category.id = product_cont.category1_id AND category.online_custshop = 1 ";
      $filter_query .= "JOIN product_cont_content ON product_cont_content.product_cont_id = product_cont.id ";
      $filter_query .= "WHERE product_cont.online = 1 ";
      $filter_query .= "ORDER BY product_cont.sort ASC ";

      return $this->fillContainers($filter_query);
    }

    /**
     * @param string $filterQuery
     * @return array
     * @throws \GsdException
     */
    private function fillContainers(string $filterQuery): array {
      $query = "SELECT product_cont.*, product_cont_content.* FROM product_cont " . $filterQuery;

      $products = [];
      $result = DBConn::db_link()->query($query);
      while ($row = $result->fetch_row()) {
        $productContainer = new ProductCont();
        $productContainer->hydrate($row);

        $productContainerContent = new ProductContContent();
        $productContainerContent->hydrate($row, count(ProductCont::columns));
        $productContainer->content = $productContainerContent;

        $products[] = $productContainer;
      }

      return $products;
    }

  }