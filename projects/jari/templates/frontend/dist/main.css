/*!***************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[0].use[1]!./node_modules/sass-loader/dist/cjs.js!./node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[0].use[3]!../../projects/jari/resources/frontend/style/imports.css ***!
  \***************************************************************************************************************************************************************************************************************************************************/
@import url(https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,300;0,400;0,500;0,600;0,700;1,300;1,400;1,500;1,600;1,700&display=swap);
/*!*******************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[0].use[1]!./node_modules/sass-loader/dist/cjs.js!./node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[0].use[3]!../../projects/jari/resources/frontend/style/imports.css (1) ***!
  \*******************************************************************************************************************************************************************************************************************************************************/
@charset "UTF-8"; /* (Google) Font imports, see https://fonts.google.com/ */
@CHARSET "UTF-8";
:root {
  /* Kleuren */
  --color-white: #fff;
  --color-white-rgb: 255, 255, 255;
  --color-black: #000;
  --color-primary: #083B58;
  --color-primary-rgb: 8, 59, 88;
  --color-yellow: #F7BD16;
  --color-blue: #0A6599;
  --color-medium-blue: #619DC5;
  --color-light-blue: #b8d4f0;
  --color-gray: #f5f7fa;
  --color-bg: #fff;
  --color-text: var(--bb-color-primary);
  --color-heading: var(--bb-color-primary);
  --color-permalink: var(--bb-color-text);
  --color-permalink-highlight: var(--bb-color-yellow);
  --color-on-primary: #fff;
  --color-error: #f00;
  --color-white: #fff;
  --color-placeholder: rgba(44, 51, 54, .5);
  --color-copyright: rgba(255, 255, 255, .5);
  --color-bg-success: #3c763d;
  --color-border-success: #d6e9c6;
  --color-bg-danger: #a94442;
  --color-border-danger: #ebccd1;
  /* Fonts */
  --font-sans: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif;
  --font-serif: Georgia, Cambria, "Times New Roman", Times, serif;
  --font-primary: "Poppins", sans-serif;
  --font-icomoon: "icomoon", fantasy;
  --h1-size: 49px;
  --h2-size: 31px;
  --h3-size: 20px;
  --h4-size: 18px;
  --h5-size: 15px;
  --h6-size: 12px;
  --body-size: 17px;
  /* Spacing */
  --spacing-heading: 20px;
  --padding: clamp(20px, calc(7.29vw - 60px), 80px);
  /* Transition */
  --transition: all .3s ease;
  /* animate.css */
  --animate-delay: 0.25s;
  /* Most used colors */
  /* --primary-color: ; */
  /* --secondary-color: ; */
  /* Button colors */
  /* --gsd-btn-bg-color: ; */
  /* --gsd-btn-text-color: ; */
  /* --gsd-btn-bg-color-hover: ; */
  /* --gsd-btn-text-color-hover: ; */
  /* --gsd-btn-primary-bg-color: ; */
  /* --gsd-btn-primary-text-color: ; */
  /* --gsd-btn-primary-bg-color-hover: ; */
  /* --gsd-btn-primary-text-color-hover: ; */
  /* --gsd-btn-secondary-bg-color: ; */
  /* --gsd-btn-secondary-text-color: ; */
  /* --gsd-btn-secondary-bg-color-hover: ; */
  /* --gsd-btn-secondary-text-color-hover: ; */
  /* --gsd-btn-tertiary-bg-color: ; */
  /* --gsd-btn-tertiary-text-color: ; */
  /* --gsd-btn-tertiary-bg-color-hover: ; */
  /* --gsd-btn-tertiary-text-color-hover: ; */
}
@media only screen and (max-width: 1024px) {
  :root {
    --h1-size: 42px;
    --h2-size: 28px;
  }
}
@media only screen and (max-width: 480px) {
  :root {
    --h1-size: 37px;
    --h2-size: 26px;
    --h3-size: 20px;
    --h4-size: 16px;
    --body-size: 16px;
  }
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  border: 0;
  outline: none;
}

html {
  display: block;
  font-family: var(--font-primary);
  font-size: var(--body-size);
  line-height: 1.5;
  font-weight: 300;
}

body {
  position: relative;
  color: var(--color-primary);
}
body .no-scroll {
  overflow: hidden;
}
body h1, body h2, body h3, body h4, body h5, body h6 {
  color: var(--color-heading);
  margin-bottom: var(--spacing-heading);
  line-height: 1.2;
  font-weight: 500;
}
body h1 {
  font-size: var(--h1-size);
}
body h2 {
  font-size: var(--h2-size);
}
body h3 {
  font-size: var(--h3-size);
}
body h4 {
  font-size: var(--h4-size);
}
body h5 {
  font-size: var(--h5-size);
}
body h6 {
  font-size: var(--h6-size);
}

input, textarea, select {
  font-family: inherit;
}

ul {
  list-style: none;
  padding: 0;
}

a {
  color: var(--color-primary);
  text-decoration: none;
  transition: var(--transition);
}
a:hover {
  color: var(--color-yellow);
}
a:hover .btn-primary, a:hover .application-form input[type=file]::-webkit-file-upload-button, .application-form a:hover input[type=file]::-webkit-file-upload-button, a:hover .main a, .main a:hover a, a:hover .top-nav ul li:last-child a, .top-nav ul li:last-child a:hover a, a:hover .other-pages li:last-child a, .other-pages li:last-child a:hover a, a:hover .btn-secondary {
  color: var(--color-white);
  background: var(--color-yellow);
}
a svg, a path {
  fill: var(--color-primary);
  transition: fill 0.2s ease-in-out;
}
a:hover svg, a:hover path {
  fill: var(--color-yellow);
}

hr {
  border-bottom: 1px solid rgba(10, 101, 153, 0.2);
  width: 100%;
}

.btn-primary, .application-form input[type=file]::-webkit-file-upload-button, .main a, .top-nav ul li:last-child a, .other-pages li:last-child a, .btn-secondary {
  display: inline-flex;
  align-content: center;
  justify-content: center;
  gap: 5px 10px;
  text-align: center;
  font-weight: 400;
  background: var(--color-primary);
  color: var(--color-white);
  padding: 0.5em 1.5em;
  border-radius: 10px;
  transition: 0.2s ease-in-out;
  cursor: pointer;
  font-size: var(--body-size);
}
.btn-primary:hover, .application-form input[type=file]:hover::-webkit-file-upload-button, .main a:hover, .top-nav ul li:last-child a:hover, .other-pages li:last-child a:hover, .btn-secondary:hover {
  color: var(--color-white);
  background: var(--color-yellow);
}
.btn-primary svg, .application-form input[type=file]::-webkit-file-upload-button svg, .main a svg, .top-nav ul li:last-child a svg, .other-pages li:last-child a svg, .btn-secondary svg, .btn-primary path, .application-form input[type=file]::-webkit-file-upload-button path, .main a path, .top-nav ul li:last-child a path, .other-pages li:last-child a path, .btn-secondary path {
  fill: var(--color-white);
  transition: fill 0.2s ease-in-out;
}
.btn-primary:hover svg, .application-form input[type=file]:hover::-webkit-file-upload-button svg, .main a:hover svg, .top-nav ul li:last-child a:hover svg, .other-pages li:last-child a:hover svg, .btn-secondary:hover svg, .btn-primary:hover path, .application-form input[type=file]:hover::-webkit-file-upload-button path, .main a:hover path, .top-nav ul li:last-child a:hover path, .other-pages li:last-child a:hover path, .btn-secondary:hover path {
  fill: var(--color-white);
}

.btn-secondary {
  background: var(--color-white);
  color: var(--color-primary);
  border: 1px solid var(--color-primary);
}
.btn-secondary:hover {
  background: var(--color-white);
  color: var(--color-primary);
  border-color: var(--color-yellow);
}

.lightbox img {
  aspect-ratio: 5/4;
  -o-object-fit: cover;
  object-fit: cover;
  cursor: pointer;
  transition: transform 0.2s ease-in-out, filter 0.2s ease-in-out;
}
.lightbox img:hover {
  filter: brightness(0.7);
}

/* Header */
:root {
  --logo-height: 29px;
  --header-height: 156px;
}
@media only screen and (max-width: 480px) {
  :root {
    --logo-height: 24px;
  }
}
@media only screen and (max-width: 1180px) {
  :root {
    --header-height: 65px;
  }
}

header {
  position: sticky;
  top: 0;
  width: 100%;
  background: white;
  transition-duration: 0.25s;
  transition-property: all;
  transition-timing-function: ease;
  transition-delay: 0s;
  z-index: 5;
  opacity: 1;
  /* Nav */
}
header.hidden {
  transform: translateY(100%);
  opacity: 0;
}
header .show-touch-devices {
  display: none;
}
header nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 1;
}
header nav .logo {
  height: var(--logo-height);
  display: flex;
  align-items: center;
}
@media only screen and (max-width: 768px) {
  header nav .logo {
    margin-right: auto;
  }
}
header nav .logo img {
  height: 100%;
  width: auto;
}
header nav ul {
  list-style: none;
  display: flex;
  align-items: center;
}
header nav ul li a {
  color: #000;
  font-weight: 400;
  text-align: center;
}
@media only screen and (max-width: 1180px) {
  header .show-touch-devices {
    display: block;
  }
}
header .top-nav {
  padding: 0 var(--padding);
  min-height: calc(var(--header-height) / 2);
}
@media only screen and (max-width: 1180px) {
  header .top-nav {
    min-height: var(--header-height);
  }
}
header .top-nav .top-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  max-width: 1400px;
  margin: auto;
}
header .top-nav .top-container ul {
  gap: 2em;
}
@media screen and (max-width: 75em) {
  header .top-nav .top-container ul {
    display: none;
  }
}
header .top-nav .top-container ul li a {
  font-size: 14px;
}
header .bottom-nav {
  background-color: var(--color-gray);
}
header .bottom-nav .bottom-container {
  display: flex;
  align-items: center;
  font-size: 0.9em;
  gap: 16px;
  margin: auto;
  width: 100%;
  max-width: 1400px;
  /* Toon menu bij hover */
}
@media only screen and (max-width: 1500px) {
  header .bottom-nav .bottom-container {
    padding-inline: 32px;
  }
}
header .bottom-nav .bottom-container li {
  cursor: pointer;
  display: flex;
  align-items: center;
  word-break: break-all;
}
header .bottom-nav .bottom-container li.dropdown {
  position: relative;
  border-radius: 10px;
  background-color: var(--color-blue);
  transition: background-color 0.2s ease-in-out;
}
header .bottom-nav .bottom-container li.dropdown a {
  padding: 20px var(--padding);
  white-space: nowrap;
}
header .bottom-nav .bottom-container li.dropdown * {
  color: var(--color-white);
}
header .bottom-nav .bottom-container li.dropdown:hover {
  background-color: var(--color-yellow);
}
header .bottom-nav .bottom-container li.dropdown .all-product-pages {
  display: grid;
  grid-template-columns: auto auto;
  position: absolute;
  top: 100%;
  left: 0;
  padding: 30px 25px;
  border-top: none;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
  background-color: var(--color-gray);
  gap: 0;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s;
  overflow: auto;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}
header .bottom-nav .bottom-container li.dropdown .all-product-pages * {
  color: var(--color-black);
}
header .bottom-nav .bottom-container li.dropdown .all-product-pages *:hover {
  color: var(--color-yellow);
}
header .bottom-nav .bottom-container .dropdown:hover .all-product-pages,
header .bottom-nav .bottom-container .dropdown:focus-within .all-product-pages {
  opacity: 1;
  visibility: visible;
}

header.hidden {
  transform: translateY(-200px);
  opacity: 0;
}

.here0 {
  color: var(--color-yellow);
  font-weight: 700;
} /* Hamburger (For smaller screens) */
.hamburger-button {
  background: none;
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--color-primary);
  transition: var(--transition);
}
.hamburger-button.hamburger-active .hamburger-button__inner i:nth-child(1) {
  transform: rotate(45deg);
  top: 9px;
}
.hamburger-button.hamburger-active .hamburger-button__inner i:nth-child(2) {
  top: 7px;
  width: 0;
  left: 50%;
}
.hamburger-button.hamburger-active .hamburger-button__inner i:nth-child(3) {
  transform: rotate(-45deg);
  bottom: 5px;
  width: 100%;
}
.hamburger-button .hamburger-button__inner {
  width: 20px;
  height: 16px;
  position: relative;
}
.hamburger-button .hamburger-button__inner i {
  left: 0;
  width: 100%;
  height: 2px;
  background: var(--color-primary);
  position: absolute;
  transition-duration: 0.25s;
  transition-property: all;
  transition-timing-function: ease;
  transition-delay: 0s;
  display: block;
}
.hamburger-button .hamburger-button__inner i:nth-child(1) {
  top: 0;
}
.hamburger-button .hamburger-button__inner i:nth-child(2) {
  top: 7px;
}
.hamburger-button .hamburger-button__inner i:nth-child(3) {
  bottom: 0;
  width: 13px;
  left: auto;
  right: 0;
}

/* Menu bar (appears after clicking hamburger) */
.menubar {
  position: fixed;
  top: 0;
  margin-top: var(--header-height);
  left: -100%;
  opacity: 0;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  height: 100%;
  background: rgb(255, 255, 255);
  transition-duration: 0.25s;
  transition-property: all;
  transition-timing-function: ease;
  transition-delay: 0s;
  z-index: 4;
  width: 100%;
}
.menubar.active {
  left: 0 !important;
  box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
  opacity: 100%;
}
.menubar * {
  text-align: center;
}
.menubar > div {
  height: calc(100dvh - var(--header-height));
  width: 100%;
  overflow-y: auto;
}
.menubar > div .dropdown {
  position: sticky;
  top: 0;
  background-color: var(--color-blue);
  transition: background-color 0.2s ease-in-out;
  padding: 1.5em 4em;
}
.menubar > div .dropdown.active {
  background-color: var(--color-yellow);
}
.menubar > div .dropdown * {
  color: var(--color-white);
}
.menubar > div .dropdown .all-product-pages {
  position: fixed;
  top: 0;
  display: block;
  opacity: 0;
  left: -100%;
  padding: 40px 25px;
  overflow-y: auto;
  height: calc(100vh - var(--header-height) * 2);
  margin-top: calc(var(--header-height) + 72px);
  background-color: var(--color-gray);
  transition-duration: 0.25s;
  transition-property: all;
  transition-timing-function: ease;
  transition-delay: 0s;
}
.menubar > div .dropdown .all-product-pages.active {
  left: 0 !important;
  box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
  opacity: 100%;
}
.menubar > div .dropdown .all-product-pages div {
  padding: 2em 4em;
}
.menubar > div .dropdown .all-product-pages * {
  color: var(--color-black);
}

.menubar ul li {
  margin-bottom: 1em;
}

.product-pages {
  background-color: var(--color-gray);
  padding: 2em 4em;
}

.other-pages {
  background-color: var(--color-white);
  padding: 2em 4em;
}

@media screen and (max-width: 75em) {
  .hamburger {
    display: block;
  }
  .top-nav > ul {
    display: none;
  }
  .bottom-nav {
    display: none !important;
  }
  body:has(.menubar.active) {
    overflow: hidden;
  }
}
@media screen and (min-width: 75em) {
  .menubar {
    left: -100% !important;
  }
  .menubar .dropdown .all-product-pages {
    left: -100% !important;
  }
}
@media screen and (max-width: 30em) {
  .menubar {
    width: 100% !important;
  }
  .menubar .dropdown .all-product-pages {
    width: 100% !important;
  }
}
footer {
  background-color: var(--color-primary);
  color: var(--color-white);
  padding: 3em 5% 1em;
  margin: 0 1%;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}
footer img {
  height: 2em;
  width: auto;
}
footer a {
  color: var(--color-white);
}
footer > div {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}
footer > div div {
  margin: 0.5em;
}
footer strong {
  display: block;
  margin-bottom: 1em;
}
footer .info-box {
  max-width: 1400px;
  width: 100%;
  margin: auto;
  padding-bottom: 32px;
}
footer hr {
  border: none;
  height: 0.5px;
  background-color: var(--color-copyright);
  width: 100%;
}
footer .copyright {
  font-size: 14px;
}
footer .copyright div, footer .copyright a:not(:hover) {
  color: var(--color-copyright);
}
footer .copyright ul {
  display: flex;
  justify-content: center;
  gap: 1em;
}

@media (max-width: 40em) {
  footer {
    text-align: center;
  }
  footer > div {
    display: flex;
    flex-direction: column;
  }
}
.breadcrumbs-wrapper {
  background-color: var(--color-primary);
}
.breadcrumbs-wrapper .breadcrumbs {
  max-width: 1400px;
  padding-inline: 34px;
  margin: auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 87px;
  font-size: 13px;
  letter-spacing: 0.03em;
}
@media only screen and (max-width: 768px) {
  .breadcrumbs-wrapper .breadcrumbs {
    min-height: 50px;
  }
}
@media only screen and (max-width: 1400px) {
  .breadcrumbs-wrapper .breadcrumbs {
    padding: 0 var(--padding);
  }
}
.breadcrumbs-wrapper .breadcrumbs a, .breadcrumbs-wrapper .breadcrumbs span, .breadcrumbs-wrapper .breadcrumbs svg, .breadcrumbs-wrapper .breadcrumbs path {
  fill: var(--color-white);
  color: var(--color-white);
}
.breadcrumbs-wrapper .breadcrumbs a {
  display: flex;
}
.breadcrumbs-wrapper .breadcrumbs a span {
  display: flex;
  align-items: center;
}
.breadcrumbs-wrapper .breadcrumbs a:hover {
  color: var(--color-yellow);
}
.breadcrumbs-wrapper .breadcrumbs a:hover svg path {
  fill: var(--color-yellow);
}
.breadcrumbs-wrapper .breadcrumbs .crumbs {
  display: flex;
  align-items: center;
  gap: 1em;
}
@media only screen and (max-width: 768px) {
  .breadcrumbs-wrapper .breadcrumbs .crumbs {
    display: none;
  }
}
.breadcrumbs-wrapper .breadcrumbs .crumbs span.gsd-svg-icon {
  display: flex;
  align-items: center;
}
.breadcrumbs-wrapper .breadcrumbs .crumbs .active-crumb {
  color: var(--color-yellow);
}
.breadcrumbs-wrapper .breadcrumbs .back span {
  margin-right: 1em;
}

@media (max-width: 768px) {
  .breadcrumbs div:first-child {
    display: none;
  }
}
.gsd-editor section.gsd-container {
  overflow: hidden;
}
.gsd-editor section.gsd-container.bg-gray {
  background-color: var(--color-gray);
}
.gsd-editor section.gsd-container.header-block {
  color: var(--color-white);
  background-color: var(--color-primary);
  padding-bottom: 3%;
  position: relative;
  z-index: 1;
}
@media only screen and (max-width: 768px) {
  .gsd-editor section.gsd-container.header-block {
    padding-top: 15px;
  }
}
.gsd-editor section.gsd-container.header-block.small-block {
  padding: 15px 0;
}
@media only screen and (min-width: 1300px) {
  .gsd-editor section.gsd-container.header-block:not(.small-block) > div {
    align-items: flex-start;
  }
  .gsd-editor section.gsd-container.header-block:not(.small-block) > div:after {
    content: "";
    background-color: var(--color-gray);
    position: absolute;
    left: 0;
    bottom: -1px;
    height: 13%;
    width: 100vw;
  }
}
@media only screen and (max-width: 768px) {
  .gsd-editor section.gsd-container.header-block:not(.small-block) > div {
    padding-inline: 20px;
  }
  .gsd-editor section.gsd-container.header-block:not(.small-block) > div:after {
    content: "";
    background-color: var(--color-gray);
    position: absolute;
    left: 0;
    bottom: -1px;
    height: max(4%, 65px);
    width: 100vw;
  }
}
.gsd-editor section.gsd-container.header-block > div {
  max-width: 1400px;
  padding-inline: 34px;
  margin: 0 auto;
  align-items: center;
}
.gsd-editor section.gsd-container.header-block > div div.gsdblock-wysiwyg-block,
.gsd-editor section.gsd-container.header-block > div div.gsdblock-image-block {
  z-index: 2;
  margin-right: 0;
}
.gsd-editor section.gsd-container.header-block > div .gsdblock-wysiwyg-block {
  align-self: center;
  width: min(625px, 58%);
}
@media only screen and (max-width: 768px) {
  .gsd-editor section.gsd-container.header-block > div .gsdblock-wysiwyg-block {
    width: 100%;
  }
}
.gsd-editor section.gsd-container.header-block > div div.gsdblock-image-block {
  width: 42%;
  aspect-ratio: 620/725;
}
@media only screen and (max-width: 768px) {
  .gsd-editor section.gsd-container.header-block > div div.gsdblock-image-block {
    width: 100%;
    max-width: 520px;
  }
}
.gsd-editor section.gsd-container.header-block > div div.gsdblock-image-block img {
  -o-object-position: center center;
  object-position: center center;
  height: 100%;
  width: 100%;
  min-height: 100%;
  min-width: 100%;
  max-height: 100%;
  max-width: 100%;
  display: block;
  -o-object-fit: cover;
  object-fit: cover;
}
.gsd-editor section.gsd-container.header-block a {
  color: var(--color-white);
}
.gsd-editor section.gsd-container.header-block a:hover {
  color: var(--color-yellow);
}

.gsd-editor {
  width: 100%;
  background-color: white;
}

.gsd-container {
  width: 100%;
  margin: 0 auto;
  padding: 50px 0;
  word-break: break-word;
}
@media only screen and (max-width: 768px) {
  .gsd-container {
    padding: 30px 0;
  }
}

.gsd-container > div {
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-inline: 34px;
  max-width: 1140px;
  width: 100%;
  gap: 30px;
}

.gsd-wide-container > div {
  max-width: 1400px;
  padding-inline: 34px;
  margin: 0 auto;
}

/** two column grids **/
.gsd-container.gsd-wysiwyg-wysiwyg > div > div,
.gsd-container.gsd-wysiwyg-image > div > div,
.gsd-container.gsd-image-wysiwyg > div > div,
.gsd-container.gsd-image-image > div > div {
  width: 50%;
} /** three column grids **/
.gsd-container.gsd-wysiwyg-wysiwyg-wysiwyg > div > div,
.gsd-container.gsd-image-image-image > div > div {
  flex: 33.33%;
} /** some default styling **/
.gsd-container.gsd-image-wysiwyg div .gsdblock-image-block:not(.parralax),
.gsd-container.gsd-image-image div .gsdblock-image-block:not(.parralax),
.gsd-container.gsd-wysiwyg-image div .gsdblock-image-block:not(.parralax) {
  width: 42%;
}
@media only screen and (max-width: 768px) {
  .gsd-container.gsd-image-wysiwyg div .gsdblock-image-block:not(.parralax),
  .gsd-container.gsd-image-image div .gsdblock-image-block:not(.parralax),
  .gsd-container.gsd-wysiwyg-image div .gsdblock-image-block:not(.parralax) {
    width: 100%;
  }
}
.gsd-container.gsd-image-wysiwyg div .gsdblock-image-block img,
.gsd-container.gsd-image-image div .gsdblock-image-block img,
.gsd-container.gsd-wysiwyg-image div .gsdblock-image-block img {
  width: 100%;
  height: auto;
  max-width: 100%;
  border-radius: 10px;
}

.gsdblock-image-block.image-block-backgroundimage {
  min-height: 250px;
}

.image-block-backgroundimage {
  background-size: cover;
  background-position: center;
}

.gsd-wysiwyg .gsdblock-wysiwyg-block p:last-child {
  margin-block-end: 0;
}

.gsd-wysiwyg-image .gsdblock-wysiwyg-block {
  margin-right: 50px;
}

.gsd-image-wysiwyg .gsdblock-wysiwyg-block {
  margin-left: 50px;
}

p {
  margin-top: 0;
  margin-bottom: 1em;
}

.image-block-title {
  padding: 10px 15px;
  font-weight: bold;
  background-color: #ebebeb;
}

.image-block-description {
  padding: 10px 15px;
  border: 1px solid #ebebeb;
}

.text-middle-width > div {
  max-width: 760px;
}

.equal-width-columns > div {
  gap: 3em;
}
.equal-width-columns > div > div {
  flex: 1;
  width: 100%;
}

/** responsive **/
@media (max-width: 767px) {
  .gsd-editor {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  .gsd-container > div {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 20px;
    padding-inline: 34px;
  }
  /** two column grids **/
  /** three column grids **/
}
@media only screen and (max-width: 767px) and (max-width: 768px) {
  .gsd-container > div {
    padding-inline: 20px;
  }
}
@media (max-width: 767px) {
  .gsd-container > div div {
    margin: 0 !important;
  }
  .gsd-container.gsd-wysiwyg-wysiwyg > div > div,
  .gsd-container.gsd-wysiwyg-image > div > div,
  .gsd-container.gsd-image-wysiwyg > div > div,
  .gsd-container.gsd-image-image > div > div {
    width: 100%;
  }
  .gsd-container.gsd-wysiwyg-wysiwyg-wysiwyg > div > div,
  .gsd-container.gsd-image-image-image > div > div {
    flex: 100%;
  }
}
main {
  width: 100%;
}

.inventory-image {
  position: relative;
  flex: 1;
  aspect-ratio: 4/7;
  height: 100%;
  -o-object-fit: cover;
  object-fit: cover;
  overflow: hidden;
  border-radius: 10px;
}
.inventory-image .info {
  color: var(--color-white);
  position: absolute;
  bottom: 3em;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.inventory-image img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
  object-fit: cover;
  transition: transform 0.2s ease-in-out;
}
.inventory-image:hover img {
  transform: scale(1.05);
}
.inventory-image:hover .btn-primary, .inventory-image:hover .application-form input[type=file]::-webkit-file-upload-button, .application-form .inventory-image:hover input[type=file]::-webkit-file-upload-button, .inventory-image:hover .main a, .main .inventory-image:hover a, .inventory-image:hover .btn-secondary, .inventory-image:hover .top-nav ul li:last-child a, .top-nav ul li:last-child .inventory-image:hover a, .inventory-image:hover .other-pages li:last-child a, .other-pages li:last-child .inventory-image:hover a {
  background-color: var(--color-yellow);
}

.parralax {
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
  border-radius: 10px;
}
.parralax img {
  width: 100%;
  -o-object-fit: cover;
  object-fit: cover;
}
.parralax .link {
  position: absolute;
  color: var(--color-white);
  top: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  z-index: 1;
}
.parralax .link svg, .parralax .link path {
  fill: var(--color-white);
}

.promotion {
  aspect-ratio: 3/1;
}
.promotion iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.promotion .overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(var(--color-white-rgb), 0.5);
  transition: opacity 0.3s ease-in-out;
}
.promotion:hover svg, .promotion:hover path {
  fill: var(--color-white);
}
.promotion:hover .btn-primary, .promotion:hover .application-form input[type=file]::-webkit-file-upload-button, .application-form .promotion:hover input[type=file]::-webkit-file-upload-button, .promotion:hover .main a, .main .promotion:hover a, .promotion:hover .btn-secondary, .promotion:hover .top-nav ul li:last-child a, .top-nav ul li:last-child .promotion:hover a, .promotion:hover .other-pages li:last-child a, .other-pages li:last-child .promotion:hover a {
  background-color: var(--color-yellow);
}
.promotion:hover .overlay {
  opacity: 0;
}

.question {
  aspect-ratio: 3/1;
}
.question img {
  position: relative;
}
.question .overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(var(--color-primary-rgb), 0.5);
}

.main a {
  background: var(--color-white);
  color: var(--color-primary);
}
.main a:hover {
  color: var(--color-white);
}

.modal {
  display: none;
  position: fixed;
  z-index: 3;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.4);
}

.modal-content {
  margin: 15% auto;
  width: 60%;
  aspect-ratio: 16/9;
}
.modal-content iframe {
  width: 100%;
  height: 100%;
}

.close {
  float: right;
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;
}

@media (max-width: 768px) {
  .gsdblock-image-block {
    aspect-ratio: 3/2;
  }
  .modal-content {
    width: 100%;
  }
}
.contact #contact-form > div {
  max-width: 972px;
  flex-direction: column;
  padding-inline: 0;
}
.contact #contact-form > div > div {
  width: 100%;
  background-color: var(--color-white);
  padding: 50px 75px;
  border-radius: 10px;
}
@media only screen and (max-width: 768px) {
  .contact #contact-form > div > div {
    padding: 35px 20px;
  }
}
.contact #contact-form > div > div .gsd-container {
  padding: 0;
  text-align: start;
}
.contact .gsd-container {
  margin: 0 auto;
}
.contact .header-block > div > div {
  width: 50%;
}
.contact .row {
  display: flex;
  flex-wrap: wrap;
  gap: 5em;
  margin: 3em 0;
}

@media (max-width: 768px) {
  .row {
    flex-direction: column;
    align-items: center;
  }
}
.application-form {
  display: flex;
  flex-direction: column;
  gap: 40px;
  max-width: 972px;
  margin: auto;
  text-align: start;
}
@media only screen and (max-width: 768px) {
  .application-form {
    gap: 25px;
  }
}
.application-form .btn-primary, .application-form input[type=file]::-webkit-file-upload-button, .application-form .btn-secondary, .application-form .top-nav ul li:last-child a, .top-nav ul li:last-child .application-form a, .application-form .other-pages li:last-child a, .other-pages li:last-child .application-form a, .application-form .main a, .main .application-form a {
  border: none;
  cursor: pointer;
}
.application-form .form-row {
  display: flex;
  gap: 54px;
}
@media only screen and (max-width: 768px) {
  .application-form .form-row {
    gap: 25px;
  }
}
.application-form .form-group {
  display: flex;
  flex: 1;
  flex-direction: column;
}
.application-form .form-group select {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.application-form .form-group label {
  font-weight: 500;
  margin-bottom: 15px;
  font-size: 15px;
  color: var(--color-black);
}
@media only screen and (max-width: 768px) {
  .application-form .form-group label {
    margin-bottom: 5px;
  }
}
.application-form .form-input {
  padding: 1.3em;
  outline: none;
  font-size: 14px;
  transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  border: 1px solid transparent;
  border-radius: 10px;
  background-color: var(--color-gray);
}
.application-form .form-input::-moz-placeholder {
  font-family: var(--font-primary);
}
.application-form .form-input::placeholder {
  font-family: var(--font-primary);
}
.application-form .form-input:hover {
  border: 1px solid #ccc;
  border-color: var(--color-black);
}
.application-form .form-input:focus {
  border: 1px solid #ccc;
  border-color: var(--color-black);
  box-shadow: var(--color-black) 0 0 2.5px;
}
.application-form .form-input.inputerror {
  border: 1px solid var(--color-error);
  box-shadow: var(--color-error) 0 0 2.5px;
}
.application-form input[type=file] {
  cursor: pointer;
}
.application-form input[type=file]::-webkit-file-upload-button {
  border: none;
  margin-right: 1em;
  cursor: pointer;
}
.application-form .alert {
  font-size: 0.8em;
  color: var(--color-error);
}

/* Textarea styling */
textarea.form-input {
  min-height: 100px;
  resize: vertical;
}

.custom-checkbox {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  width: 22px;
  height: 22px;
  border: 1px solid var(--color-primary);
  border-radius: 4px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s ease-in-out, border-color 0.2s ease-in-out;
  background-color: white;
  position: relative;
  /* Checkmark (✔) na selectie */
}
.custom-checkbox:hover {
  border-color: var(--color-yellow);
}
.custom-checkbox:checked {
  background-color: var(--color-yellow);
  border-color: var(--color-yellow);
}
.custom-checkbox:before {
  content: "";
  width: 14px;
  height: 14px;
  display: block;
}
.custom-checkbox:after {
  content: "✔";
  font-size: 16px;
  color: var(--color-white);
  font-weight: bold;
  position: absolute;
  line-height: 1;
}
.custom-checkbox.inputerror {
  border: 1px solid var(--color-error);
  box-shadow: var(--color-error) 0 0 2.5px;
}

.privacy {
  margin-top: 1em;
  display: flex;
  align-items: center;
  gap: 10px;
}
.privacy label {
  cursor: pointer;
  font-weight: 600;
  font-size: 14px;
}

.form-row:has(.privacy) {
  align-items: center;
  justify-content: space-between;
}

.warning {
  font-size: 12px;
} /* Responsive Design */
@media (max-width: 600px) {
  .form-row {
    flex-direction: column;
    gap: 10px;
  }
}
#flashmessage {
  background-color: var(--color-primary);
}
#flashmessage #message-container {
  width: 100%;
  margin: auto;
  color: var(--color-white);
  text-align: center;
  display: flex;
  flex-direction: column;
}
#flashmessage #message-container .alert-wrapper.alert-success {
  color: var(--color-white);
  background-color: var(--color-bg-success);
  border-color: var(--color-border-success);
}
#flashmessage #message-container .alert-wrapper.alert-danger {
  color: var(--color-white);
  background-color: var(--color-bg-danger);
  border-color: var(--color-border-danger);
}
#flashmessage #message-container .alert-wrapper .alert {
  max-width: 1400px;
  margin: auto;
  padding: 5px;
}
#flashmessage #message-container .alert-wrapper .alert .alert-message {
  display: block;
}

#cookie-banner {
  display: none;
  z-index: 999999999;
  overflow-y: auto;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}
#cookie-banner > div {
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  height: 100dvh;
}
#cookie-banner > div .cookie-glass {
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  position: fixed;
  background-color: black;
  opacity: 0.8;
}
#cookie-banner > div .cookie-glass > div {
  opacity: 0.5;
  background-color: black;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}
#cookie-banner > div .cookie-container {
  width: min(500px, 100%);
  max-height: 100%;
  display: inline-block;
  vertical-align: middle;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
  --tw-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  box-shadow: 0 0 rgba(0, 0, 0, 0), 0 0 rgba(0, 0, 0, 0), var(--tw-shadow);
  text-align: left;
  border-radius: 0.5rem;
  overflow: auto;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translateX(var(--tw-translate-x)) translateY(var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
#cookie-banner > div .cookie-container .cookie-header {
  position: sticky;
  top: 0;
  text-align: center;
  padding: 25px;
  background-color: var(--color-primary);
  color: white;
  display: flex;
}
#cookie-banner > div .cookie-container .cookie-header > img {
  width: 120px;
}
#cookie-banner > div .cookie-container .cookie-header .cookie-title {
  margin-left: auto;
  display: flex;
  align-items: center;
}
#cookie-banner > div .cookie-container .cookie-header .cookie-title > svg {
  margin-right: 12px;
}
#cookie-banner > div .cookie-container .cookie-header .cookie-title h2 {
  margin: 0;
  display: none;
}
#cookie-banner > div .cookie-container .cookie-content {
  background: white;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
#cookie-banner > div .cookie-container .cookie-content .cookie-about {
  padding: 24px;
}
#cookie-banner > div .cookie-container .cookie-content .cookie-about a {
  text-decoration: underline;
}
#cookie-banner > div .cookie-container .cookie-content .cookie-btns {
  position: sticky;
  bottom: 0;
  background-color: white;
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  justify-content: space-evenly;
  gap: 4px;
  padding: 0 15px 15px 15px;
  border-top: 1px solid var(--light-brown);
}
#cookie-banner > div .cookie-container .cookie-content .cookie-btns button {
  cursor: pointer;
  flex-grow: 1;
  width: 100%;
  padding: 12px 4px;
  border-radius: 10px;
}
#cookie-banner > div .cookie-container .cookie-content .cookie-btns button#consent-deny {
  background-color: unset;
}
#cookie-banner > div .cookie-container .cookie-content .cookie-btns button#consent-deny:hover {
  color: var(--color-yellow);
}

#tooltip {
  z-index: 10000000000000000;
  background-color: unset;
  border-radius: 4px;
}
#tooltip #tooltip-content {
  background-color: var(--brown);
  color: white;
  border-radius: 4px;
}
#tooltip #tooltip-arrow,
#tooltip #tooltip-arrow::before {
  background-color: var(--brown);
  border: none !important;
}

@media screen and (min-width: 830px) {
  #cookie-banner > div .cookie-container {
    max-height: 80%;
  }
  #cookie-banner > div .cookie-container .cookie-header .cookie-title h2 {
    display: inline;
  }
  #cookie-banner > div .cookie-container .cookie-content .cookie-btns button {
    width: -moz-fit-content;
    width: fit-content;
  }
} /*!
* animate.css - https://animate.style/
* Version - 4.1.1
* Licensed under the MIT license - http://opensource.org/licenses/MIT
*
* Copyright (c) 2020 Animate.css
*/
:root {
  --animate-duration: 1s;
  --animate-delay: 1s;
  --animate-repeat: 1;
}

.animate__animated {
  animation-duration: 1s;
  animation-duration: var(--animate-duration);
  animation-fill-mode: both;
}

.animate__animated.animate__infinite {
  animation-iteration-count: infinite;
}

.animate__animated.animate__repeat-1 {
  animation-iteration-count: 1;
  animation-iteration-count: var(--animate-repeat);
}

.animate__animated.animate__repeat-2 {
  animation-iteration-count: 2;
  animation-iteration-count: calc(var(--animate-repeat) * 2);
}

.animate__animated.animate__repeat-3 {
  animation-iteration-count: 3;
  animation-iteration-count: calc(var(--animate-repeat) * 3);
}

.animate__animated.animate__delay-1s {
  animation-delay: 1s;
  animation-delay: var(--animate-delay);
}

.animate__animated.animate__delay-2s {
  animation-delay: 2s;
  animation-delay: calc(var(--animate-delay) * 2);
}

.animate__animated.animate__delay-3s {
  animation-delay: 3s;
  animation-delay: calc(var(--animate-delay) * 3);
}

.animate__animated.animate__delay-4s {
  animation-delay: 4s;
  animation-delay: calc(var(--animate-delay) * 4);
}

.animate__animated.animate__delay-5s {
  animation-delay: 5s;
  animation-delay: calc(var(--animate-delay) * 5);
}

.animate__animated.animate__faster {
  animation-duration: 0.5s;
  animation-duration: calc(var(--animate-duration) / 2);
}

.animate__animated.animate__fast {
  animation-duration: 0.8s;
  animation-duration: calc(var(--animate-duration) * 0.8);
}

.animate__animated.animate__slow {
  animation-duration: 2s;
  animation-duration: calc(var(--animate-duration) * 2);
}

.animate__animated.animate__slower {
  animation-duration: 3s;
  animation-duration: calc(var(--animate-duration) * 3);
}

@media print, (prefers-reduced-motion: reduce) {
  .animate__animated {
    animation-duration: 1ms !important;
    transition-duration: 1ms !important;
    animation-iteration-count: 1 !important;
  }
  .animate__animated[class*=Out] {
    opacity: 0;
  }
} /* Attention seekers  */
@keyframes bounce {
  from, 20%, 53%, to {
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    transform: translate3d(0, -30px, 0) scaleY(1.1);
  }
  70% {
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    transform: translate3d(0, -15px, 0) scaleY(1.05);
  }
  80% {
    transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    transform: translate3d(0, 0, 0) scaleY(0.95);
  }
  90% {
    transform: translate3d(0, -4px, 0) scaleY(1.02);
  }
}
.animate__bounce {
  animation-name: bounce;
  transform-origin: center bottom;
}

@keyframes flash {
  from, 50%, to {
    opacity: 1;
  }
  25%, 75% {
    opacity: 0;
  }
}
.animate__flash {
  animation-name: flash;
} /* originally authored by Nick Pettit - https://github.com/nickpettit/glide */
@keyframes pulse {
  from {
    transform: scale3d(1, 1, 1);
  }
  50% {
    transform: scale3d(1.05, 1.05, 1.05);
  }
  to {
    transform: scale3d(1, 1, 1);
  }
}
.animate__pulse {
  animation-name: pulse;
  animation-timing-function: ease-in-out;
}

@keyframes rubberBand {
  from {
    transform: scale3d(1, 1, 1);
  }
  30% {
    transform: scale3d(1.25, 0.75, 1);
  }
  40% {
    transform: scale3d(0.75, 1.25, 1);
  }
  50% {
    transform: scale3d(1.15, 0.85, 1);
  }
  65% {
    transform: scale3d(0.95, 1.05, 1);
  }
  75% {
    transform: scale3d(1.05, 0.95, 1);
  }
  to {
    transform: scale3d(1, 1, 1);
  }
}
.animate__rubberBand {
  animation-name: rubberBand;
}

@keyframes shakeX {
  from, to {
    transform: translate3d(0, 0, 0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translate3d(-10px, 0, 0);
  }
  20%, 40%, 60%, 80% {
    transform: translate3d(10px, 0, 0);
  }
}
.animate__shakeX {
  animation-name: shakeX;
}

@keyframes shakeY {
  from, to {
    transform: translate3d(0, 0, 0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translate3d(0, -10px, 0);
  }
  20%, 40%, 60%, 80% {
    transform: translate3d(0, 10px, 0);
  }
}
.animate__shakeY {
  animation-name: shakeY;
}

@keyframes headShake {
  0% {
    transform: translateX(0);
  }
  6.5% {
    transform: translateX(-6px) rotateY(-9deg);
  }
  18.5% {
    transform: translateX(5px) rotateY(7deg);
  }
  31.5% {
    transform: translateX(-3px) rotateY(-5deg);
  }
  43.5% {
    transform: translateX(2px) rotateY(3deg);
  }
  50% {
    transform: translateX(0);
  }
}
.animate__headShake {
  animation-timing-function: ease-in-out;
  animation-name: headShake;
}

@keyframes swing {
  20% {
    transform: rotate3d(0, 0, 1, 15deg);
  }
  40% {
    transform: rotate3d(0, 0, 1, -10deg);
  }
  60% {
    transform: rotate3d(0, 0, 1, 5deg);
  }
  80% {
    transform: rotate3d(0, 0, 1, -5deg);
  }
  to {
    transform: rotate3d(0, 0, 1, 0deg);
  }
}
.animate__swing {
  transform-origin: top center;
  animation-name: swing;
}

@keyframes tada {
  from {
    transform: scale3d(1, 1, 1);
  }
  10%, 20% {
    transform: scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg);
  }
  30%, 50%, 70%, 90% {
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
  }
  40%, 60%, 80% {
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
  }
  to {
    transform: scale3d(1, 1, 1);
  }
}
.animate__tada {
  animation-name: tada;
} /* originally authored by Nick Pettit - https://github.com/nickpettit/glide */
@keyframes wobble {
  from {
    transform: translate3d(0, 0, 0);
  }
  15% {
    transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg);
  }
  30% {
    transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg);
  }
  45% {
    transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg);
  }
  60% {
    transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg);
  }
  75% {
    transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg);
  }
  to {
    transform: translate3d(0, 0, 0);
  }
}
.animate__wobble {
  animation-name: wobble;
}

@keyframes jello {
  from, 11.1%, to {
    transform: translate3d(0, 0, 0);
  }
  22.2% {
    transform: skewX(-12.5deg) skewY(-12.5deg);
  }
  33.3% {
    transform: skewX(6.25deg) skewY(6.25deg);
  }
  44.4% {
    transform: skewX(-3.125deg) skewY(-3.125deg);
  }
  55.5% {
    transform: skewX(1.5625deg) skewY(1.5625deg);
  }
  66.6% {
    transform: skewX(-0.78125deg) skewY(-0.78125deg);
  }
  77.7% {
    transform: skewX(0.390625deg) skewY(0.390625deg);
  }
  88.8% {
    transform: skewX(-0.1953125deg) skewY(-0.1953125deg);
  }
}
.animate__jello {
  animation-name: jello;
  transform-origin: center;
}

@keyframes heartBeat {
  0% {
    transform: scale(1);
  }
  14% {
    transform: scale(1.3);
  }
  28% {
    transform: scale(1);
  }
  42% {
    transform: scale(1.3);
  }
  70% {
    transform: scale(1);
  }
}
.animate__heartBeat {
  animation-name: heartBeat;
  animation-duration: 1.3s;
  animation-duration: calc(var(--animate-duration) * 1.3);
  animation-timing-function: ease-in-out;
} /* Back entrances */
@keyframes backInDown {
  0% {
    transform: translateY(-1200px) scale(0.7);
    opacity: 0.7;
  }
  80% {
    transform: translateY(0px) scale(0.7);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}
.animate__backInDown {
  animation-name: backInDown;
}

@keyframes backInLeft {
  0% {
    transform: translateX(-2000px) scale(0.7);
    opacity: 0.7;
  }
  80% {
    transform: translateX(0px) scale(0.7);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}
.animate__backInLeft {
  animation-name: backInLeft;
}

@keyframes backInRight {
  0% {
    transform: translateX(2000px) scale(0.7);
    opacity: 0.7;
  }
  80% {
    transform: translateX(0px) scale(0.7);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}
.animate__backInRight {
  animation-name: backInRight;
}

@keyframes backInUp {
  0% {
    transform: translateY(1200px) scale(0.7);
    opacity: 0.7;
  }
  80% {
    transform: translateY(0px) scale(0.7);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}
.animate__backInUp {
  animation-name: backInUp;
} /* Back exits */
@keyframes backOutDown {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  20% {
    transform: translateY(0px) scale(0.7);
    opacity: 0.7;
  }
  100% {
    transform: translateY(700px) scale(0.7);
    opacity: 0.7;
  }
}
.animate__backOutDown {
  animation-name: backOutDown;
}

@keyframes backOutLeft {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  20% {
    transform: translateX(0px) scale(0.7);
    opacity: 0.7;
  }
  100% {
    transform: translateX(-2000px) scale(0.7);
    opacity: 0.7;
  }
}
.animate__backOutLeft {
  animation-name: backOutLeft;
}

@keyframes backOutRight {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  20% {
    transform: translateX(0px) scale(0.7);
    opacity: 0.7;
  }
  100% {
    transform: translateX(2000px) scale(0.7);
    opacity: 0.7;
  }
}
.animate__backOutRight {
  animation-name: backOutRight;
}

@keyframes backOutUp {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  20% {
    transform: translateY(0px) scale(0.7);
    opacity: 0.7;
  }
  100% {
    transform: translateY(-700px) scale(0.7);
    opacity: 0.7;
  }
}
.animate__backOutUp {
  animation-name: backOutUp;
} /* Bouncing entrances  */
@keyframes bounceIn {
  from, 20%, 40%, 60%, 80%, to {
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  0% {
    opacity: 0;
    transform: scale3d(0.3, 0.3, 0.3);
  }
  20% {
    transform: scale3d(1.1, 1.1, 1.1);
  }
  40% {
    transform: scale3d(0.9, 0.9, 0.9);
  }
  60% {
    opacity: 1;
    transform: scale3d(1.03, 1.03, 1.03);
  }
  80% {
    transform: scale3d(0.97, 0.97, 0.97);
  }
  to {
    opacity: 1;
    transform: scale3d(1, 1, 1);
  }
}
.animate__bounceIn {
  animation-duration: 0.75s;
  animation-duration: calc(var(--animate-duration) * 0.75);
  animation-name: bounceIn;
}

@keyframes bounceInDown {
  from, 60%, 75%, 90%, to {
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  0% {
    opacity: 0;
    transform: translate3d(0, -3000px, 0) scaleY(3);
  }
  60% {
    opacity: 1;
    transform: translate3d(0, 25px, 0) scaleY(0.9);
  }
  75% {
    transform: translate3d(0, -10px, 0) scaleY(0.95);
  }
  90% {
    transform: translate3d(0, 5px, 0) scaleY(0.985);
  }
  to {
    transform: translate3d(0, 0, 0);
  }
}
.animate__bounceInDown {
  animation-name: bounceInDown;
}

@keyframes bounceInLeft {
  from, 60%, 75%, 90%, to {
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  0% {
    opacity: 0;
    transform: translate3d(-3000px, 0, 0) scaleX(3);
  }
  60% {
    opacity: 1;
    transform: translate3d(25px, 0, 0) scaleX(1);
  }
  75% {
    transform: translate3d(-10px, 0, 0) scaleX(0.98);
  }
  90% {
    transform: translate3d(5px, 0, 0) scaleX(0.995);
  }
  to {
    transform: translate3d(0, 0, 0);
  }
}
.animate__bounceInLeft {
  animation-name: bounceInLeft;
}

@keyframes bounceInRight {
  from, 60%, 75%, 90%, to {
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  from {
    opacity: 0;
    transform: translate3d(3000px, 0, 0) scaleX(3);
  }
  60% {
    opacity: 1;
    transform: translate3d(-25px, 0, 0) scaleX(1);
  }
  75% {
    transform: translate3d(10px, 0, 0) scaleX(0.98);
  }
  90% {
    transform: translate3d(-5px, 0, 0) scaleX(0.995);
  }
  to {
    transform: translate3d(0, 0, 0);
  }
}
.animate__bounceInRight {
  animation-name: bounceInRight;
}

@keyframes bounceInUp {
  from, 60%, 75%, 90%, to {
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  from {
    opacity: 0;
    transform: translate3d(0, 3000px, 0) scaleY(5);
  }
  60% {
    opacity: 1;
    transform: translate3d(0, -20px, 0) scaleY(0.9);
  }
  75% {
    transform: translate3d(0, 10px, 0) scaleY(0.95);
  }
  90% {
    transform: translate3d(0, -5px, 0) scaleY(0.985);
  }
  to {
    transform: translate3d(0, 0, 0);
  }
}
.animate__bounceInUp {
  animation-name: bounceInUp;
} /* Bouncing exits  */
@keyframes bounceOut {
  20% {
    transform: scale3d(0.9, 0.9, 0.9);
  }
  50%, 55% {
    opacity: 1;
    transform: scale3d(1.1, 1.1, 1.1);
  }
  to {
    opacity: 0;
    transform: scale3d(0.3, 0.3, 0.3);
  }
}
.animate__bounceOut {
  animation-duration: 0.75s;
  animation-duration: calc(var(--animate-duration) * 0.75);
  animation-name: bounceOut;
}

@keyframes bounceOutDown {
  20% {
    transform: translate3d(0, 10px, 0) scaleY(0.985);
  }
  40%, 45% {
    opacity: 1;
    transform: translate3d(0, -20px, 0) scaleY(0.9);
  }
  to {
    opacity: 0;
    transform: translate3d(0, 2000px, 0) scaleY(3);
  }
}
.animate__bounceOutDown {
  animation-name: bounceOutDown;
}

@keyframes bounceOutLeft {
  20% {
    opacity: 1;
    transform: translate3d(20px, 0, 0) scaleX(0.9);
  }
  to {
    opacity: 0;
    transform: translate3d(-2000px, 0, 0) scaleX(2);
  }
}
.animate__bounceOutLeft {
  animation-name: bounceOutLeft;
}

@keyframes bounceOutRight {
  20% {
    opacity: 1;
    transform: translate3d(-20px, 0, 0) scaleX(0.9);
  }
  to {
    opacity: 0;
    transform: translate3d(2000px, 0, 0) scaleX(2);
  }
}
.animate__bounceOutRight {
  animation-name: bounceOutRight;
}

@keyframes bounceOutUp {
  20% {
    transform: translate3d(0, -10px, 0) scaleY(0.985);
  }
  40%, 45% {
    opacity: 1;
    transform: translate3d(0, 20px, 0) scaleY(0.9);
  }
  to {
    opacity: 0;
    transform: translate3d(0, -2000px, 0) scaleY(3);
  }
}
.animate__bounceOutUp {
  animation-name: bounceOutUp;
} /* Fading entrances  */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
.animate__fadeIn {
  animation-name: fadeIn;
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translate3d(0, -100%, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}
.animate__fadeInDown {
  animation-name: fadeInDown;
}

@keyframes fadeInDownBig {
  from {
    opacity: 0;
    transform: translate3d(0, -2000px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}
.animate__fadeInDownBig {
  animation-name: fadeInDownBig;
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translate3d(-100%, 0, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}
.animate__fadeInLeft {
  animation-name: fadeInLeft;
}

@keyframes fadeInLeftBig {
  from {
    opacity: 0;
    transform: translate3d(-2000px, 0, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}
.animate__fadeInLeftBig {
  animation-name: fadeInLeftBig;
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translate3d(100%, 0, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}
.animate__fadeInRight {
  animation-name: fadeInRight;
}

@keyframes fadeInRightBig {
  from {
    opacity: 0;
    transform: translate3d(2000px, 0, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}
.animate__fadeInRightBig {
  animation-name: fadeInRightBig;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translate3d(0, 100%, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}
.animate__fadeInUp {
  animation-name: fadeInUp;
}

@keyframes fadeInUpBig {
  from {
    opacity: 0;
    transform: translate3d(0, 2000px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}
.animate__fadeInUpBig {
  animation-name: fadeInUpBig;
}

@keyframes fadeInTopLeft {
  from {
    opacity: 0;
    transform: translate3d(-100%, -100%, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}
.animate__fadeInTopLeft {
  animation-name: fadeInTopLeft;
}

@keyframes fadeInTopRight {
  from {
    opacity: 0;
    transform: translate3d(100%, -100%, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}
.animate__fadeInTopRight {
  animation-name: fadeInTopRight;
}

@keyframes fadeInBottomLeft {
  from {
    opacity: 0;
    transform: translate3d(-100%, 100%, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}
.animate__fadeInBottomLeft {
  animation-name: fadeInBottomLeft;
}

@keyframes fadeInBottomRight {
  from {
    opacity: 0;
    transform: translate3d(100%, 100%, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}
.animate__fadeInBottomRight {
  animation-name: fadeInBottomRight;
} /* Fading exits */
@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
.animate__fadeOut {
  animation-name: fadeOut;
}

@keyframes fadeOutDown {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    transform: translate3d(0, 100%, 0);
  }
}
.animate__fadeOutDown {
  animation-name: fadeOutDown;
}

@keyframes fadeOutDownBig {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    transform: translate3d(0, 2000px, 0);
  }
}
.animate__fadeOutDownBig {
  animation-name: fadeOutDownBig;
}

@keyframes fadeOutLeft {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    transform: translate3d(-100%, 0, 0);
  }
}
.animate__fadeOutLeft {
  animation-name: fadeOutLeft;
}

@keyframes fadeOutLeftBig {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    transform: translate3d(-2000px, 0, 0);
  }
}
.animate__fadeOutLeftBig {
  animation-name: fadeOutLeftBig;
}

@keyframes fadeOutRight {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    transform: translate3d(100%, 0, 0);
  }
}
.animate__fadeOutRight {
  animation-name: fadeOutRight;
}

@keyframes fadeOutRightBig {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    transform: translate3d(2000px, 0, 0);
  }
}
.animate__fadeOutRightBig {
  animation-name: fadeOutRightBig;
}

@keyframes fadeOutUp {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    transform: translate3d(0, -100%, 0);
  }
}
.animate__fadeOutUp {
  animation-name: fadeOutUp;
}

@keyframes fadeOutUpBig {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    transform: translate3d(0, -2000px, 0);
  }
}
.animate__fadeOutUpBig {
  animation-name: fadeOutUpBig;
}

@keyframes fadeOutTopLeft {
  from {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
  to {
    opacity: 0;
    transform: translate3d(-100%, -100%, 0);
  }
}
.animate__fadeOutTopLeft {
  animation-name: fadeOutTopLeft;
}

@keyframes fadeOutTopRight {
  from {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
  to {
    opacity: 0;
    transform: translate3d(100%, -100%, 0);
  }
}
.animate__fadeOutTopRight {
  animation-name: fadeOutTopRight;
}

@keyframes fadeOutBottomRight {
  from {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
  to {
    opacity: 0;
    transform: translate3d(100%, 100%, 0);
  }
}
.animate__fadeOutBottomRight {
  animation-name: fadeOutBottomRight;
}

@keyframes fadeOutBottomLeft {
  from {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
  to {
    opacity: 0;
    transform: translate3d(-100%, 100%, 0);
  }
}
.animate__fadeOutBottomLeft {
  animation-name: fadeOutBottomLeft;
} /* Flippers */
@keyframes flip {
  from {
    transform: perspective(400px) scale3d(1, 1, 1) translate3d(0, 0, 0) rotate3d(0, 1, 0, -360deg);
    animation-timing-function: ease-out;
  }
  40% {
    transform: perspective(400px) scale3d(1, 1, 1) translate3d(0, 0, 150px) rotate3d(0, 1, 0, -190deg);
    animation-timing-function: ease-out;
  }
  50% {
    transform: perspective(400px) scale3d(1, 1, 1) translate3d(0, 0, 150px) rotate3d(0, 1, 0, -170deg);
    animation-timing-function: ease-in;
  }
  80% {
    transform: perspective(400px) scale3d(0.95, 0.95, 0.95) translate3d(0, 0, 0) rotate3d(0, 1, 0, 0deg);
    animation-timing-function: ease-in;
  }
  to {
    transform: perspective(400px) scale3d(1, 1, 1) translate3d(0, 0, 0) rotate3d(0, 1, 0, 0deg);
    animation-timing-function: ease-in;
  }
}
.animate__animated.animate__flip {
  backface-visibility: visible;
  animation-name: flip;
}

@keyframes flipInX {
  from {
    transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    animation-timing-function: ease-in;
    opacity: 0;
  }
  40% {
    transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    animation-timing-function: ease-in;
  }
  60% {
    transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
    opacity: 1;
  }
  80% {
    transform: perspective(400px) rotate3d(1, 0, 0, -5deg);
  }
  to {
    transform: perspective(400px);
  }
}
.animate__flipInX {
  backface-visibility: visible !important;
  animation-name: flipInX;
}

@keyframes flipInY {
  from {
    transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
    animation-timing-function: ease-in;
    opacity: 0;
  }
  40% {
    transform: perspective(400px) rotate3d(0, 1, 0, -20deg);
    animation-timing-function: ease-in;
  }
  60% {
    transform: perspective(400px) rotate3d(0, 1, 0, 10deg);
    opacity: 1;
  }
  80% {
    transform: perspective(400px) rotate3d(0, 1, 0, -5deg);
  }
  to {
    transform: perspective(400px);
  }
}
.animate__flipInY {
  backface-visibility: visible !important;
  animation-name: flipInY;
}

@keyframes flipOutX {
  from {
    transform: perspective(400px);
  }
  30% {
    transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    opacity: 1;
  }
  to {
    transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    opacity: 0;
  }
}
.animate__flipOutX {
  animation-duration: 0.75s;
  animation-duration: calc(var(--animate-duration) * 0.75);
  animation-name: flipOutX;
  backface-visibility: visible !important;
}

@keyframes flipOutY {
  from {
    transform: perspective(400px);
  }
  30% {
    transform: perspective(400px) rotate3d(0, 1, 0, -15deg);
    opacity: 1;
  }
  to {
    transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
    opacity: 0;
  }
}
.animate__flipOutY {
  animation-duration: 0.75s;
  animation-duration: calc(var(--animate-duration) * 0.75);
  backface-visibility: visible !important;
  animation-name: flipOutY;
} /* Lightspeed */
@keyframes lightSpeedInRight {
  from {
    transform: translate3d(100%, 0, 0) skewX(-30deg);
    opacity: 0;
  }
  60% {
    transform: skewX(20deg);
    opacity: 1;
  }
  80% {
    transform: skewX(-5deg);
  }
  to {
    transform: translate3d(0, 0, 0);
  }
}
.animate__lightSpeedInRight {
  animation-name: lightSpeedInRight;
  animation-timing-function: ease-out;
}

@keyframes lightSpeedInLeft {
  from {
    transform: translate3d(-100%, 0, 0) skewX(30deg);
    opacity: 0;
  }
  60% {
    transform: skewX(-20deg);
    opacity: 1;
  }
  80% {
    transform: skewX(5deg);
  }
  to {
    transform: translate3d(0, 0, 0);
  }
}
.animate__lightSpeedInLeft {
  animation-name: lightSpeedInLeft;
  animation-timing-function: ease-out;
}

@keyframes lightSpeedOutRight {
  from {
    opacity: 1;
  }
  to {
    transform: translate3d(100%, 0, 0) skewX(30deg);
    opacity: 0;
  }
}
.animate__lightSpeedOutRight {
  animation-name: lightSpeedOutRight;
  animation-timing-function: ease-in;
}

@keyframes lightSpeedOutLeft {
  from {
    opacity: 1;
  }
  to {
    transform: translate3d(-100%, 0, 0) skewX(-30deg);
    opacity: 0;
  }
}
.animate__lightSpeedOutLeft {
  animation-name: lightSpeedOutLeft;
  animation-timing-function: ease-in;
} /* Rotating entrances */
@keyframes rotateIn {
  from {
    transform: rotate3d(0, 0, 1, -200deg);
    opacity: 0;
  }
  to {
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}
.animate__rotateIn {
  animation-name: rotateIn;
  transform-origin: center;
}

@keyframes rotateInDownLeft {
  from {
    transform: rotate3d(0, 0, 1, -45deg);
    opacity: 0;
  }
  to {
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}
.animate__rotateInDownLeft {
  animation-name: rotateInDownLeft;
  transform-origin: left bottom;
}

@keyframes rotateInDownRight {
  from {
    transform: rotate3d(0, 0, 1, 45deg);
    opacity: 0;
  }
  to {
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}
.animate__rotateInDownRight {
  animation-name: rotateInDownRight;
  transform-origin: right bottom;
}

@keyframes rotateInUpLeft {
  from {
    transform: rotate3d(0, 0, 1, 45deg);
    opacity: 0;
  }
  to {
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}
.animate__rotateInUpLeft {
  animation-name: rotateInUpLeft;
  transform-origin: left bottom;
}

@keyframes rotateInUpRight {
  from {
    transform: rotate3d(0, 0, 1, -90deg);
    opacity: 0;
  }
  to {
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}
.animate__rotateInUpRight {
  animation-name: rotateInUpRight;
  transform-origin: right bottom;
} /* Rotating exits */
@keyframes rotateOut {
  from {
    opacity: 1;
  }
  to {
    transform: rotate3d(0, 0, 1, 200deg);
    opacity: 0;
  }
}
.animate__rotateOut {
  animation-name: rotateOut;
  transform-origin: center;
}

@keyframes rotateOutDownLeft {
  from {
    opacity: 1;
  }
  to {
    transform: rotate3d(0, 0, 1, 45deg);
    opacity: 0;
  }
}
.animate__rotateOutDownLeft {
  animation-name: rotateOutDownLeft;
  transform-origin: left bottom;
}

@keyframes rotateOutDownRight {
  from {
    opacity: 1;
  }
  to {
    transform: rotate3d(0, 0, 1, -45deg);
    opacity: 0;
  }
}
.animate__rotateOutDownRight {
  animation-name: rotateOutDownRight;
  transform-origin: right bottom;
}

@keyframes rotateOutUpLeft {
  from {
    opacity: 1;
  }
  to {
    transform: rotate3d(0, 0, 1, -45deg);
    opacity: 0;
  }
}
.animate__rotateOutUpLeft {
  animation-name: rotateOutUpLeft;
  transform-origin: left bottom;
}

@keyframes rotateOutUpRight {
  from {
    opacity: 1;
  }
  to {
    transform: rotate3d(0, 0, 1, 90deg);
    opacity: 0;
  }
}
.animate__rotateOutUpRight {
  animation-name: rotateOutUpRight;
  transform-origin: right bottom;
} /* Specials */
@keyframes hinge {
  0% {
    animation-timing-function: ease-in-out;
  }
  20%, 60% {
    transform: rotate3d(0, 0, 1, 80deg);
    animation-timing-function: ease-in-out;
  }
  40%, 80% {
    transform: rotate3d(0, 0, 1, 60deg);
    animation-timing-function: ease-in-out;
    opacity: 1;
  }
  to {
    transform: translate3d(0, 700px, 0);
    opacity: 0;
  }
}
.animate__hinge {
  animation-duration: 2s;
  animation-duration: calc(var(--animate-duration) * 2);
  animation-name: hinge;
  transform-origin: top left;
}

@keyframes jackInTheBox {
  from {
    opacity: 0;
    transform: scale(0.1) rotate(30deg);
    transform-origin: center bottom;
  }
  50% {
    transform: rotate(-10deg);
  }
  70% {
    transform: rotate(3deg);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
.animate__jackInTheBox {
  animation-name: jackInTheBox;
} /* originally authored by Nick Pettit - https://github.com/nickpettit/glide */
@keyframes rollIn {
  from {
    opacity: 0;
    transform: translate3d(-100%, 0, 0) rotate3d(0, 0, 1, -120deg);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}
.animate__rollIn {
  animation-name: rollIn;
} /* originally authored by Nick Pettit - https://github.com/nickpettit/glide */
@keyframes rollOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    transform: translate3d(100%, 0, 0) rotate3d(0, 0, 1, 120deg);
  }
}
.animate__rollOut {
  animation-name: rollOut;
} /* Zooming entrances */
@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale3d(0.3, 0.3, 0.3);
  }
  50% {
    opacity: 1;
  }
}
.animate__zoomIn {
  animation-name: zoomIn;
}

@keyframes zoomInDown {
  from {
    opacity: 0;
    transform: scale3d(0.1, 0.1, 0.1) translate3d(0, -1000px, 0);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  }
  60% {
    opacity: 1;
    transform: scale3d(0.475, 0.475, 0.475) translate3d(0, 60px, 0);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
  }
}
.animate__zoomInDown {
  animation-name: zoomInDown;
}

@keyframes zoomInLeft {
  from {
    opacity: 0;
    transform: scale3d(0.1, 0.1, 0.1) translate3d(-1000px, 0, 0);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  }
  60% {
    opacity: 1;
    transform: scale3d(0.475, 0.475, 0.475) translate3d(10px, 0, 0);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
  }
}
.animate__zoomInLeft {
  animation-name: zoomInLeft;
}

@keyframes zoomInRight {
  from {
    opacity: 0;
    transform: scale3d(0.1, 0.1, 0.1) translate3d(1000px, 0, 0);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  }
  60% {
    opacity: 1;
    transform: scale3d(0.475, 0.475, 0.475) translate3d(-10px, 0, 0);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
  }
}
.animate__zoomInRight {
  animation-name: zoomInRight;
}

@keyframes zoomInUp {
  from {
    opacity: 0;
    transform: scale3d(0.1, 0.1, 0.1) translate3d(0, 1000px, 0);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  }
  60% {
    opacity: 1;
    transform: scale3d(0.475, 0.475, 0.475) translate3d(0, -60px, 0);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
  }
}
.animate__zoomInUp {
  animation-name: zoomInUp;
} /* Zooming exits */
@keyframes zoomOut {
  from {
    opacity: 1;
  }
  50% {
    opacity: 0;
    transform: scale3d(0.3, 0.3, 0.3);
  }
  to {
    opacity: 0;
  }
}
.animate__zoomOut {
  animation-name: zoomOut;
}

@keyframes zoomOutDown {
  40% {
    opacity: 1;
    transform: scale3d(0.475, 0.475, 0.475) translate3d(0, -60px, 0);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  }
  to {
    opacity: 0;
    transform: scale3d(0.1, 0.1, 0.1) translate3d(0, 2000px, 0);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
  }
}
.animate__zoomOutDown {
  animation-name: zoomOutDown;
  transform-origin: center bottom;
}

@keyframes zoomOutLeft {
  40% {
    opacity: 1;
    transform: scale3d(0.475, 0.475, 0.475) translate3d(42px, 0, 0);
  }
  to {
    opacity: 0;
    transform: scale(0.1) translate3d(-2000px, 0, 0);
  }
}
.animate__zoomOutLeft {
  animation-name: zoomOutLeft;
  transform-origin: left center;
}

@keyframes zoomOutRight {
  40% {
    opacity: 1;
    transform: scale3d(0.475, 0.475, 0.475) translate3d(-42px, 0, 0);
  }
  to {
    opacity: 0;
    transform: scale(0.1) translate3d(2000px, 0, 0);
  }
}
.animate__zoomOutRight {
  animation-name: zoomOutRight;
  transform-origin: right center;
}

@keyframes zoomOutUp {
  40% {
    opacity: 1;
    transform: scale3d(0.475, 0.475, 0.475) translate3d(0, 60px, 0);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  }
  to {
    opacity: 0;
    transform: scale3d(0.1, 0.1, 0.1) translate3d(0, -2000px, 0);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
  }
}
.animate__zoomOutUp {
  animation-name: zoomOutUp;
  transform-origin: center bottom;
} /* Sliding entrances */
@keyframes slideInDown {
  from {
    transform: translate3d(0, -100%, 0);
    visibility: visible;
  }
  to {
    transform: translate3d(0, 0, 0);
  }
}
.animate__slideInDown {
  animation-name: slideInDown;
}

@keyframes slideInLeft {
  from {
    transform: translate3d(-100%, 0, 0);
    visibility: visible;
  }
  to {
    transform: translate3d(0, 0, 0);
  }
}
.animate__slideInLeft {
  animation-name: slideInLeft;
}

@keyframes slideInRight {
  from {
    transform: translate3d(100%, 0, 0);
    visibility: visible;
  }
  to {
    transform: translate3d(0, 0, 0);
  }
}
.animate__slideInRight {
  animation-name: slideInRight;
}

@keyframes slideInUp {
  from {
    transform: translate3d(0, 100%, 0);
    visibility: visible;
  }
  to {
    transform: translate3d(0, 0, 0);
  }
}
.animate__slideInUp {
  animation-name: slideInUp;
} /* Sliding exits */
@keyframes slideOutDown {
  from {
    transform: translate3d(0, 0, 0);
  }
  to {
    visibility: hidden;
    transform: translate3d(0, 100%, 0);
  }
}
.animate__slideOutDown {
  animation-name: slideOutDown;
}

@keyframes slideOutLeft {
  from {
    transform: translate3d(0, 0, 0);
  }
  to {
    visibility: hidden;
    transform: translate3d(-100%, 0, 0);
  }
}
.animate__slideOutLeft {
  animation-name: slideOutLeft;
}

@keyframes slideOutRight {
  from {
    transform: translate3d(0, 0, 0);
  }
  to {
    visibility: hidden;
    transform: translate3d(100%, 0, 0);
  }
}
.animate__slideOutRight {
  animation-name: slideOutRight;
}

@keyframes slideOutUp {
  from {
    transform: translate3d(0, 0, 0);
  }
  to {
    visibility: hidden;
    transform: translate3d(0, -100%, 0);
  }
}
.animate__slideOutUp {
  animation-name: slideOutUp;
}

.animate__animated {
  visibility: hidden;
}

@keyframes fadeInUpJari {
  from {
    opacity: 0;
    transform: translate3d(0, 20%, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}
.animate__fadeInUpJari {
  animation-name: fadeInUpJari;
}

@media (prefers-reduced-motion: reduce) {
  .animate__animated {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}
#login, #reset, #new-user {
  display: flex;
  align-items: center;
  flex-direction: column;
  padding: 4em 0;
  background-color: var(--color-gray);
}
#login > div, #reset > div, #new-user > div {
  width: 500px;
  border-radius: 10px;
}
#login > div h2, #reset > div h2, #new-user > div h2 {
  text-align: center;
  padding: 0.5em;
}
#login > div .application-form, #reset > div .application-form, #new-user > div .application-form {
  gap: 2em;
  padding: 2em;
  background-color: var(--color-white);
  border-radius: 10px;
}
#login > div .application-form .submit input, #reset > div .application-form .submit input, #new-user > div .application-form .submit input {
  width: 100%;
  padding: 12px 24px;
}
#login > div .application-form .links, #reset > div .application-form .links, #new-user > div .application-form .links {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
@media only screen and (max-width: 768px) {
  #login > div .application-form .links, #reset > div .application-form .links, #new-user > div .application-form .links {
    flex-direction: column;
    gap: 8px;
  }
}
#login .alert, #reset .alert, #new-user .alert {
  font-size: 0.8em;
  color: var(--color-error);
}
#login #cancel-forgotten, #reset #cancel-forgotten, #new-user #cancel-forgotten {
  text-align: center;
}

#new-user > div {
  width: 800px;
}

@media (max-width: 600px) {
  #login, #reset, #new-user {
    margin: 0 1%;
  }
  #login > div, #reset > div, #new-user > div {
    width: 100%;
  }
}
#searchbar {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-inline: 24px auto;
}
@media only screen and (max-width: 1200px) {
  #searchbar {
    margin-inline: auto 16px;
  }
}
#searchbar > button {
  display: flex;
  align-items: center;
  background: unset;
  cursor: pointer;
  margin-left: 6px;
}
#searchbar > button#search-btn-mobile {
  display: none;
}
@media only screen and (max-width: 1200px) {
  #searchbar > button#search-btn-mobile {
    display: flex;
  }
  #searchbar > button:not(#search-btn-mobile) {
    display: none;
  }
}
#searchbar > button:active svg, #searchbar > button:hover svg {
  fill: var(--color-yellow);
}
#searchbar #search-input {
  padding: 10px 16px;
  outline: none;
  font-size: 14px;
  transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  border: 1px solid #ccc;
  border-radius: 10px;
}
#searchbar #search-input::-moz-placeholder {
  font-family: var(--font-primary);
}
#searchbar #search-input::placeholder {
  font-family: var(--font-primary);
}
#searchbar #search-input:hover {
  border-color: var(--color-black);
}
#searchbar #search-input:focus {
  border-color: var(--color-black);
  box-shadow: var(--color-black) 0 0 2.5px;
}
@media only screen and (max-width: 1200px) {
  #searchbar #search-input {
    display: none;
  }
}

#search-form-mobile {
  display: none;
  width: 100%;
  padding: 6px 4px;
}
#search-form-mobile #search-input-mobile {
  width: 100%;
  padding: 10px 16px;
  outline: none;
  font-size: 14px;
  transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  border: 1px solid #ccc;
  border-radius: 10px;
}
#search-form-mobile #search-input-mobile::-moz-placeholder {
  font-family: var(--font-primary);
}
#search-form-mobile #search-input-mobile::placeholder {
  font-family: var(--font-primary);
}
#search-form-mobile #search-input-mobile:hover {
  border-color: var(--color-black);
}
#search-form-mobile #search-input-mobile:focus {
  border-color: var(--color-black);
  box-shadow: var(--color-black) 0 0 2.5px;
}

#search-results {
  background-color: var(--color-gray);
  padding-inline: 0;
}
#search-results > div {
  max-width: 972px;
}
#search-results > div #no-result {
  margin-inline: auto;
  margin-bottom: 100px;
  text-align: center;
}
#search-results > div #no-result #search-val {
  color: var(--color-yellow);
}

#page-tags .tags {
  display: block;
  max-width: 1140px;
  width: 100%;
  margin: 0 auto 24px auto;
  padding-inline: 34px;
}
#page-tags .tags > i {
  margin-right: 6px;
}
#page-tags .tags > a {
  white-space: nowrap;
}

#tag-results > div {
  display: block;
  max-width: 1140px;
  width: 100%;
  margin: 0 auto 24px auto;
  padding-inline: 34px;
}
#tag-results > div #tags_result {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 40px;
}
@media only screen and (max-width: 768px) {
  #tag-results > div #tags_result {
    grid-template-columns: 1fr;
  }
}
#tag-results > div #tags_result .tag-container {
  color: var(--color-white);
  background-color: var(--color-blue);
  transition: var(--transition);
  border-radius: 10px;
  padding: 10px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
#tag-results > div #tags_result .tag-container .teaser {
  margin-bottom: 24px;
  font-size: 15px;
}
#tag-results > div #tags_result .tag-container .readmore {
  margin-top: auto;
  color: var(--color-white);
}
#tag-results > div #tags_result .tag-container .readmore svg {
  vertical-align: middle;
  transform: translateX(0);
  transition: transform 0.2s ease-in-out;
}
#tag-results > div #tags_result .tag-container .readmore svg path {
  fill: var(--color-white);
}
#tag-results > div #tags_result .tag-container .readmore:hover svg {
  transform: translate(6px);
  transition: transform 0.3s ease-in-out;
}
#tag-results > div #tags_result .tag-container .readmore:hover svg path {
  fill: var(--color-yellow);
}
#tag-results > div #tags_result .tag-container:hover a {
  color: var(--color-white);
}
#tag-results > div #tags_result .tag-container:hover a:hover {
  color: var(--color-yellow);
}

.card {
  text-decoration: none;
  height: 100%;
  overflow: hidden;
  position: relative;
  background: #fff;
  border: 1px solid rgba(10, 101, 153, 0.2);
  border-radius: 10px;
}
.card:hover {
  border-color: var(--color-primary);
}
.card .card-image {
  height: 208px;
  padding: 20px 28px;
}
.card .card-image img {
  width: 100%;
  height: 100%;
  -o-object-fit: contain;
  object-fit: contain;
}
.card .card-description {
  height: calc(100% - 208px);
  padding: 32px 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: var(--color-gray);
}
.card .card-description h2 {
  color: var(--color-black);
}
.card .card-description * {
  font-size: 15px;
}
.card .card-description .btn-primary, .card .card-description .btn-secondary, .card .card-description .top-nav ul li:last-child a, .top-nav ul li:last-child .card .card-description a, .card .card-description .other-pages li:last-child a, .other-pages li:last-child .card .card-description a, .card .card-description .main a, .main .card .card-description a, .card .card-description .application-form input[type=file]::-webkit-file-upload-button, .application-form .card .card-description input[type=file]::-webkit-file-upload-button {
  white-space: nowrap;
}

.filter-box {
  width: 325px;
  background-color: var(--color-gray);
  font-size: 15px;
  font-weight: 400;
  color: var(--color-black);
  border-radius: 10px;
}
.filter-box .filter-title {
  font-weight: 500;
  font-size: 16px;
  padding: 20px 40px;
}
.filter-box .filters {
  padding: 25px 40px;
  border-top: 1px solid rgba(8, 59, 88, 0.2);
}
.filter-box .filters .filter {
  display: block;
  margin-bottom: 15px;
  line-height: 1.3;
  letter-spacing: 0.03em;
  color: var(--color-black);
}
.filter-box .filters .filter:hover {
  color: var(--color-yellow);
}
.filter-box .filters .filter.active {
  font-weight: 600;
  color: var(--color-blue);
}

div:has(.filter-box) {
  align-self: normal;
}

.overview {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
  width: 100%;
}
.overview .pager {
  grid-column: 1/-1;
  display: flex;
  justify-content: right;
  margin-top: 1em;
}
.overview .pager a.disabled {
  font-weight: 600;
}
.overview .pager a.disabled:hover, .overview .pager a.disabled:hover * {
  color: var(--color-primary);
  fill: var(--color-primary);
}
.overview .pager .pager-pageamount, .overview .pager i {
  display: none;
}
.overview .pager td.nextprev {
  margin-left: 1em;
  display: flex;
  gap: 0.7em;
}

div:has(.loader) {
  position: relative;
}

.loader {
  position: absolute;
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1;
  background-color: var(--color-white);
}

#seo-section > div {
  align-items: baseline;
}

@media (min-width: 640px) {
  .overview {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (min-width: 1200px) {
  .overview {
    grid-template-columns: repeat(3, 1fr);
  }
}
@media (max-width: 940px) {
  .gsd-container .filter-box {
    display: none;
  }
  .gsd-container > div {
    flex-direction: column;
  }
}
.product-image {
  aspect-ratio: 5/4;
  border-radius: 10px;
  border: 1px solid rgba(10, 101, 153, 0.2);
}
.product-image img {
  width: 100%;
  height: 100%;
  -o-object-fit: contain;
  object-fit: contain;
}

.products-attributes {
  display: flex;
  flex-wrap: wrap;
  gap: 10px 0;
  padding: 40px 45px;
  background: var(--color-gray);
  border-radius: 10px;
  margin: 1.5em 0;
}
.products-attributes .attribute-row {
  width: 50%;
  display: flex;
  padding-right: 20px;
  letter-spacing: 0.03em;
  flex-wrap: wrap;
}
.products-attributes .attribute-label {
  font-weight: 600;
  min-width: 175px;
  flex-shrink: 0;
  text-align: left;
  white-space: normal;
}
.products-attributes .attribute-value {
  flex: 1 1 auto;
  text-align: left;
  white-space: normal;
}

div:has(.products-attributes) {
  align-self: start;
}

.products-details > div:not(:last-child) {
  margin-bottom: 3em;
}

.table-responsive {
  width: 100%;
  overflow: auto;
}
.table-responsive table {
  border-collapse: collapse;
  text-align: left;
  table-layout: fixed;
  min-width: 100%;
  margin-bottom: 0.5em;
}
.table-responsive table strong {
  color: var(--color-primary);
}
.table-responsive table tbody {
  display: flex;
  flex-direction: column;
  border: 1px solid var(--color-blue);
  border-radius: 10px;
}
.table-responsive table tbody tr {
  width: 100%;
  display: flex;
  font-size: 16px;
  color: var(--color-blue);
  font-weight: 400;
  letter-spacing: 0.03em;
  border-bottom: 1px solid rgba(8, 59, 88, 0.2);
}
.table-responsive table tbody tr:first-child {
  border-bottom: 1px solid var(--color-blue);
  font-weight: 600;
  color: var(--color-primary);
}
.table-responsive table tbody td {
  flex: 1;
  border: none;
  padding: 10px 15px;
  min-width: 103px;
}
.table-responsive table tbody td:not(:last-child) {
  border-right: 1px solid rgba(8, 59, 88, 0.2);
}

.carousel {
  display: flex;
  flex-wrap: nowrap;
  overflow-x: auto;
  gap: 2rem;
  width: 100%;
  padding-bottom: 1rem;
}
.carousel .card {
  flex-shrink: 0;
  width: 300px;
  height: 100% !important;
}
.carousel .card h2 {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.table-responsive::-webkit-scrollbar, .carousel::-webkit-scrollbar {
  height: 8px;
  background-color: transparent;
}
.table-responsive::-webkit-scrollbar-thumb, .carousel::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background-color: var(--color-primary);
}

@media (max-width: 640px) {
  .products-attributes .attribute-row {
    width: 100%;
    padding-right: 0;
  }
}
